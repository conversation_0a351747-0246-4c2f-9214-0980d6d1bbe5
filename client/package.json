{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "export": "next build", "start": "next start", "lint": "next lint", "deploy": "next build && touch out/.nojekyll && git add out/ && git commit -m 'Deploy to gh-pages' && git subtree push --prefix out origin gh-pages"}, "dependencies": {"@react-icons/all-files": "^4.1.0", "framer-motion": "^12.12.1", "lucide-react": "^0.508.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}