'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useThemeColor } from '@/components/theme-color-context';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  // We're using our own color cycling instead of the theme context
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { currentColor } = useThemeColor(); // Unused but kept for consistency
  const [currentIndex, setCurrentIndex] = useState(0);

  // Define styles matching HeroSection.tsx
  const styles = [
    {
      bg: 'from-blue-50 to-blue-100',
      text: 'text-blue-600',
      button: 'bg-blue-600 hover:bg-blue-700',
      profileBg: 'bg-blue-200',
      profileText: 'text-blue-800'
    },
    {
      bg: 'from-green-50 to-green-100',
      text: 'text-green-600',
      button: 'bg-green-600 hover:bg-green-700',
      profileBg: 'bg-green-200',
      profileText: 'text-green-800'
    },
    {
      bg: 'from-purple-50 to-purple-100',
      text: 'text-purple-600',
      button: 'bg-purple-600 hover:bg-purple-700',
      profileBg: 'bg-purple-200',
      profileText: 'text-purple-800'
    },
    {
      bg: 'from-orange-50 to-orange-100',
      text: 'text-orange-600',
      button: 'bg-orange-600 hover:bg-orange-700',
      profileBg: 'bg-orange-200',
      profileText: 'text-orange-800'
    }
  ];

  // Set up color cycling
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % styles.length);
    }, 3000); // Change color every 3 seconds

    return () => clearInterval(interval);
  }, [styles.length]);

  // Get current style
  const currentStyle = styles[currentIndex];

  return (
    <footer className="relative py-12 border-t border-gray-100 overflow-hidden shadow-sm bg-white">
      {/* Background layers - lower z-index */}
      <div className="absolute inset-0 z-0">
        {/* Dynamic gradient background based on theme */}
        <div className={`absolute inset-0 bg-gradient-to-br ${currentStyle.bg} opacity-20`}></div>
        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 bg-grid-pattern opacity-3"></div>
        {/* Decorative circles */}
        <div className={`absolute -right-20 -bottom-20 w-64 h-64 rounded-full ${currentStyle.button} opacity-10`}></div>
        <div className={`absolute -left-20 -top-20 w-64 h-64 rounded-full ${currentStyle.button} opacity-10`}></div>
        {/* Additional subtle texture */}
        <div className="absolute inset-0 bg-noise opacity-[0.02]"></div>
      </div>

      {/* Decorative accent - higher z-index */}
      <div className={`absolute top-0 left-0 right-0 h-1 ${currentStyle.button} z-10 transition-colors duration-500`}></div>

      {/* Additional decorative elements */}
      <div className={`absolute top-12 right-12 w-24 h-24 rounded-full ${currentStyle.button} opacity-5 blur-xl`}></div>
      <div className={`absolute bottom-12 left-12 w-32 h-32 rounded-full ${currentStyle.button} opacity-5 blur-xl`}></div>
      <div className="container mx-auto px-4 relative z-20">
        {/* Animated gradient line */}
        <div className="relative h-0.5 w-full mb-10 overflow-hidden">
          <div className={`absolute inset-0 ${currentStyle.button} opacity-70`}></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent w-1/3 animate-shimmer"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* About Column */}
          <div className="md:col-span-1">
            <h3 className={`text-xl font-bold ${currentStyle.text} mb-4 drop-shadow-sm`}>Ankush Gupta</h3>
            <p className="text-gray-700 mb-4">
              ML Engineer & Full Stack Developer specializing in building exceptional digital experiences.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://github.com/AnkushGitRepo"
                target="_blank"
                rel="noopener noreferrer"
                className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                aria-label="GitHub"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                </svg>
              </a>
              <a
                href="https://linkedin.com/in/ankushgupta18"
                target="_blank"
                rel="noopener noreferrer"
                className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                aria-label="LinkedIn"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" />
                </svg>
              </a>
              <a
                href="https://instagram.com/_ankushg"
                target="_blank"
                rel="noopener noreferrer"
                className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                aria-label="Instagram"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                </svg>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium"
                aria-label="Email"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </a>
            </div>
          </div>

          {/* Main Links Column */}
          <div className="md:col-span-1">
            <h3 className={`text-xl font-bold ${currentStyle.text} mb-4 drop-shadow-sm`}>Main Pages</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/"
                  className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  href="/about"
                  className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                >
                  About Me
                </Link>
              </li>
              <li>
                <Link
                  href="/projects"
                  className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                >
                  Projects
                </Link>
              </li>
              <li>
                <Link
                  href="/skills"
                  className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                >
                  Skills
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                >
                  Contact Me
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources Column */}
          <div className="md:col-span-1">
            <h3 className={`text-xl font-bold ${currentStyle.text} mb-4 drop-shadow-sm`}>Resources</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/books"
                  className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                >
                  Books
                </Link>
              </li>
              <li>
                <Link
                  href="/certifications"
                  className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                >
                  Certifications
                </Link>
              </li>
              <li>
                <a
                  href="/resume.pdf"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-gray-700 hover:${currentStyle.text} transition-colors duration-300 font-medium`}
                >
                  Resume
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Column */}
          <div className="md:col-span-1">
            <h3 className={`text-xl font-bold ${currentStyle.text} mb-4 drop-shadow-sm`}>Contact</h3>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">Email:</span> <EMAIL>
            </p>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">Location:</span> Ahmedabad, Gujarat, India
            </p>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">Phone:</span> +91 7202906881
            </p>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="border-t border-gray-200 mt-8 pt-8 text-center relative">
          <div className="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
          <div className={`w-20 h-1 ${currentStyle.button} mx-auto mb-6`}></div>
          <p className="text-gray-700 font-medium">
            &copy; {currentYear} <span className={currentStyle.text}>Ankush Gupta</span>. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
