import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: 'swap',
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    template: '%s | <PERSON><PERSON><PERSON>',
    default: '<PERSON><PERSON><PERSON> | ML Engineer & Full Stack Developer',
  },
  description: 'Port<PERSON><PERSON> of <PERSON><PERSON><PERSON>, an ML Engineer and Full Stack Developer specializing in building exceptional digital experiences.',
  generator: 'Next.js',
  applicationName: 'Ankus<PERSON> Portfolio',
  referrer: 'origin-when-cross-origin',
  keywords: ['ML Engineer', 'Full Stack Developer', 'Portfolio', 'React', 'Next.js', 'Node.js', 'MongoDB', 'Express'],
  authors: [{ name: '<PERSON><PERSON><PERSON>' }],
  creator: '<PERSON><PERSON><PERSON>',
  publisher: '<PERSON><PERSON><PERSON>',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://ankushgitrepo.github.io/Portfolio'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Ankush Gupta | ML Engineer & Full Stack Developer',
    description: 'Portfolio of Ankush Gupta, an ML Engineer and Full Stack Developer specializing in building exceptional digital experiences.',
    url: 'https://ankushgitrepo.github.io/Portfolio',
    siteName: 'Ankush Gupta Portfolio',
    images: [
      {
        url: 'https://ankushgitrepo.github.io/Portfolio/images/profile_icon_image.png',
        width: 1200,
        height: 630,
        alt: 'Ankush Gupta Portfolio',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Ankush Gupta | ML Engineer & Full Stack Developer',
    description: 'Portfolio of Ankush Gupta, an ML Engineer and Full Stack Developer specializing in building exceptional digital experiences.',
    creator: '@_ankushg',
    images: ['https://ankushgitrepo.github.io/Portfolio/images/profile_icon_image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href={process.env.NODE_ENV === 'production' ? '/Portfolio/favicon.ico' : '/favicon.ico'} />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
