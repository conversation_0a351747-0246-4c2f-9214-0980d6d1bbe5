"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[907],{2907:(e,t,a)=>{a.d(t,{gp:()=>g,tx:()=>p,RS:()=>h});let r="http://localhost:5001/api";async function o(){let e=await fetch("".concat(r,"/projects/featured"));if(!e.ok)throw <PERSON>rror("Failed to fetch featured projects");return e.json()}async function i(){let e=await fetch("".concat(r,"/skills"));if(!e.ok)throw Error("Failed to fetch skills");return e.json()}async function n(e){let t=await fetch("".concat(r,"/contact"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to submit contact form");return t.json()}var c=a(5055),s=a(9434);async function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"AnkushGitRepo",t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;try{let r=await fetch("https://api.github.com/users/".concat(e,"/repos?sort=updated&per_page=100"),{headers:{Accept:"application/vnd.github.v3+json"},next:{revalidate:3600}});if(!r.ok)throw Error("GitHub API error: ".concat(r.status));let o=await r.json();t&&(o=o.filter(e=>!e.fork));let i=["MERN-Sandbox","MERN Sandbox","DSA","AnkushGitRepo","Pharmacy-Management-System","Currency_Converter_Using_Core_Java","Currency Converter"];return(o=o.filter(e=>!i.some(t=>e.name.toLowerCase()===t.toLowerCase()||e.name.toLowerCase().replace(/-/g," ")===t.toLowerCase()||e.name.toLowerCase().replace(/_/g," ")===t.toLowerCase()))).sort((e,t)=>new Date(t.pushed_at).getTime()-new Date(e.pushed_at).getTime()),o.slice(0,a)}catch(e){return console.error("Error fetching GitHub repos:",e),[]}}function d(e){return e.map(e=>{var t,a,r,o;if("Portfolio"===e.name)return{_id:e.id.toString(),title:"Portfolio Website",description:"A modern portfolio website built with Next.js and Express. This project showcases my skills in full-stack development, responsive design, and modern web technologies.\n\nThe website features a clean, minimalist design with animated backgrounds and color themes that change dynamically. It includes sections for projects, skills, and contact information.",image:"".concat((0,s.O)("/images/projects/Portfolio_1.png"),", ").concat((0,s.O)("/images/projects/Portfolio_2.png"),", ").concat((0,s.O)("/images/projects/Portfolio_3.png")),technologies:["Next.js","React","TypeScript","Express","MongoDB","Tailwind CSS"],githubUrl:e.html_url,liveUrl:e.homepage||"https://ankushgitrepo.github.io/Portfolio/",featured:!0,category:"Web Development",createdAt:e.created_at,updatedAt:e.updated_at};if("DataHarbor"===e.name)return{_id:e.id.toString(),title:"DataHarbor",description:e.description||"A comprehensive data management and analytics platform. DataHarbor provides tools for data collection, processing, and visualization.\n\nBuilt with modern technologies, this project demonstrates advanced data handling capabilities and user-friendly interfaces.",image:"".concat((0,s.O)("/images/projects/Dataharbor_1.png"),", ").concat((0,s.O)("/images/projects/Dataharbor_2.png"),", ").concat((0,s.O)("/images/projects/Dataharbor_3.png")),technologies:(null==(a=e.topics)?void 0:a.length)>0?e.topics.map(e=>e.charAt(0).toUpperCase()+e.slice(1)):e.language?[e.language]:["Python","Data Science","Analytics"],githubUrl:e.html_url,liveUrl:e.homepage||"",featured:!0,category:"Data Science",createdAt:e.created_at,updatedAt:e.updated_at};if("Resume-Builder"===e.name)return{_id:e.id.toString(),title:"Resume Builder",description:e.description||"Resume Builder is an interactive web application that helps users create professional resumes quickly and easily.",image:"".concat((0,s.O)("/images/projects/Resume_Builder_1.png"),", ").concat((0,s.O)("/images/projects/Resume_Builder_2.png"),", ").concat((0,s.O)("/images/projects/Resume_Builder_3.png")),technologies:(null==(r=e.topics)?void 0:r.length)>0?e.topics.map(e=>e.charAt(0).toUpperCase()+e.slice(1)):e.language?[e.language]:["HTML","CSS","Javascript"],githubUrl:e.html_url,liveUrl:e.homepage||"",featured:!0,category:"Web Development",createdAt:e.created_at,updatedAt:e.updated_at};if("Periodic-Table"===e.name)return{_id:e.id.toString(),title:"Periodic Table",description:e.description||"Interactive Periodic Table Explorer: Discover elements with detailed info, dynamic filters, responsive design, and dedicated pages for each element.",image:"".concat((0,s.O)("/images/projects/Periodic_Table_1.png"),", ").concat((0,s.O)("/images/projects/Periodic_Table_2.png"),", ").concat((0,s.O)("/images/projects/Periodic_Table_3.png")),technologies:(null==(o=e.topics)?void 0:o.length)>0?e.topics.map(e=>e.charAt(0).toUpperCase()+e.slice(1)):e.language?[e.language]:["HTML","CSS","Javascript"],githubUrl:e.html_url,liveUrl:e.homepage||"",featured:!0,category:"Web Development",createdAt:e.created_at,updatedAt:e.updated_at};let i=(null==(t=e.topics)?void 0:t.length)>0?e.topics.map(e=>e.charAt(0).toUpperCase()+e.slice(1)):e.language?[e.language]:["Other"],n=(0,s.O)("/images/projects/github-repo.jpg");return{_id:e.id.toString(),title:e.name.replace(/-/g," ").replace(/_/g," "),description:e.description||"A ".concat(e.language||"code"," repository."),image:n,technologies:i,githubUrl:e.html_url,liveUrl:e.homepage||"",featured:e.stargazers_count>0,category:e.language||"Other",createdAt:e.created_at,updatedAt:e.updated_at}})}let u=window.location.hostname.includes("github.io");async function p(){if(u)try{let e=await l("AnkushGitRepo",!0,6),t=e.find(e=>"Portfolio"===e.name),a=e.find(e=>"DataHarbor"===e.name),r=e.find(e=>"Resume-Builder"===e.name),o=e.find(e=>"Periodic-Table"===e.name),i=e.filter(e=>"Portfolio"!==e.name&&"DataHarbor"!==e.name&&"Resume-Builder"!==e.name&&"Periodic-Table"!==e.name),n=[];t&&n.push(t),a&&n.push(a),r&&n.push(r),o&&n.push(o);let s=d([...n,...i]),u=c.dt.filter(e=>e.featured),p=[...s];return u.forEach(e=>{s.some(t=>t.title.toLowerCase()===e.title.toLowerCase())||p.push(e)}),p.slice(0,6)}catch(e){return console.warn("Failed to fetch GitHub repos, using mock data",e),c.dt.filter(e=>e.featured)}try{return await o()}catch(e){console.warn("Failed to fetch featured projects from API, using mock data",e);try{let e=await l("AnkushGitRepo",!0,6),t=e.find(e=>"Portfolio"===e.name),a=e.find(e=>"DataHarbor"===e.name),r=e.find(e=>"Resume-Builder"===e.name),o=e.find(e=>"Periodic-Table"===e.name),i=e.filter(e=>"Portfolio"!==e.name&&"DataHarbor"!==e.name&&"Resume-Builder"!==e.name&&"Periodic-Table"!==e.name),n=[];t&&n.push(t),a&&n.push(a),r&&n.push(r),o&&n.push(o);let s=d([...n,...i]),u=c.dt.filter(e=>e.featured),p=[...s];return u.forEach(e=>{s.some(t=>t.title.toLowerCase()===e.title.toLowerCase())||p.push(e)}),p.slice(0,6)}catch(e){return console.warn("Failed to fetch GitHub repos, using mock data only",e),c.dt.filter(e=>e.featured)}}}async function g(){if(u)return c.HQ;try{return await i()}catch(e){return console.warn("Failed to fetch skills from API, using mock data",e),c.HQ}}async function h(e){if(u)return(0,c.v0)();try{return await n(e)}catch(e){return console.warn("Failed to submit contact form to API, using mock handler",e),(0,c.v0)()}}}}]);