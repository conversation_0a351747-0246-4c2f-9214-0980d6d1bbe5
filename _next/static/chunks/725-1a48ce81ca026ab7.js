"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[725],{1359:(e,t,r)=>{r.d(t,{S:()=>n,ThemeColorProvider:()=>s,g:()=>i});var a=r(5155),o=r(2115);let l=(0,o.createContext)({currentColor:"blue",setCurrentColor:()=>{}});function s(e){let{children:t}=e,[r,s]=(0,o.useState)("blue");return(0,a.jsx)(l.Provider,{value:{currentColor:r,setCurrentColor:s},children:t})}function n(){return(0,o.useContext)(l)}function i(e){return({blue:{bg:"bg-blue-600",text:"text-blue-600",border:"border-blue-600",hover:"hover:bg-blue-700",light:"bg-blue-100 text-blue-800",dark:"dark:bg-blue-900 dark:text-blue-200"},green:{bg:"bg-green-600",text:"text-green-600",border:"border-green-600",hover:"hover:bg-green-700",light:"bg-green-100 text-green-800",dark:"dark:bg-green-900 dark:text-green-200"},purple:{bg:"bg-purple-600",text:"text-purple-600",border:"border-purple-600",hover:"hover:bg-purple-700",light:"bg-purple-100 text-purple-800",dark:"dark:bg-purple-900 dark:text-purple-200"},orange:{bg:"bg-orange-600",text:"text-orange-600",border:"border-orange-600",hover:"hover:bg-orange-700",light:"bg-orange-100 text-orange-800",dark:"dark:bg-orange-900 dark:text-orange-200"}})[e]}},3725:(e,t,r)=>{r.d(t,{default:()=>x});var a=r(5155),o=r(5695),l=r(2115),s=r(6874),n=r.n(s);let i=()=>{let[e,t]=(0,l.useState)(!1),[r,s]=(0,l.useState)(!1),[i,c]=(0,l.useState)(0),d=(0,o.usePathname)(),x=[{bg:"from-blue-50 to-blue-100",text:"text-blue-600",button:"bg-blue-600 hover:bg-blue-700",profileBg:"bg-blue-200",profileText:"text-blue-800"},{bg:"from-green-50 to-green-100",text:"text-green-600",button:"bg-green-600 hover:bg-green-700",profileBg:"bg-green-200",profileText:"text-green-800"},{bg:"from-purple-50 to-purple-100",text:"text-purple-600",button:"bg-purple-600 hover:bg-purple-700",profileBg:"bg-purple-200",profileText:"text-purple-800"},{bg:"from-orange-50 to-orange-100",text:"text-orange-600",button:"bg-orange-600 hover:bg-orange-700",profileBg:"bg-orange-200",profileText:"text-orange-800"}],h=x[i];(0,l.useEffect)(()=>{let e=setInterval(()=>{c(e=>(e+1)%x.length)},3e3);return()=>clearInterval(e)},[x.length]),(0,l.useEffect)(()=>{let e=()=>{window.scrollY>50?s(!0):s(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let u=()=>{t(!1)},g=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/projects",label:"Projects"},{href:"/skills",label:"Skills"},{href:"/contact",label:"Contact"}];return(0,a.jsxs)("header",{className:"fixed top-0 left-0 w-full z-50 transition-all duration-300 bg-gradient-to-r ".concat(h.bg," ").concat(r?"backdrop-blur-md shadow-md":"bg-opacity-80"),children:[(0,a.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,a.jsx)(n(),{href:"/",className:"text-2xl font-bold ".concat(h.profileText," transition-colors duration-300"),children:"Ankush Gupta"}),(0,a.jsx)("nav",{className:"hidden md:flex space-x-8",children:g.map(e=>(0,a.jsx)(n(),{href:e.href,className:"text-lg transition-colors duration-300 ".concat(d===e.href?"".concat(h.text," font-medium"):"".concat(h.profileText," hover:").concat(h.text)),children:e.label},e.href))}),(0,a.jsx)("button",{className:"md:hidden focus:outline-none ".concat(h.profileText," transition-colors duration-300"),onClick:()=>{t(!e)},"aria-label":"Toggle menu",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 ".concat(h.text," transition-colors duration-300"),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]}),e&&(0,a.jsx)("div",{className:"md:hidden bg-gradient-to-r ".concat(h.bg," shadow-lg"),children:(0,a.jsx)("nav",{className:"flex flex-col py-4",children:g.map(e=>(0,a.jsx)(n(),{href:e.href,className:"px-4 py-3 text-lg ".concat(d===e.href?"".concat(h.text," font-medium bg-white/10"):h.profileText),onClick:u,children:e.label},e.href))})})]})};var c=r(1359);let d=()=>{let e=new Date().getFullYear(),{currentColor:t}=(0,c.S)(),[r,o]=(0,l.useState)(0),s=[{bg:"from-blue-50 to-blue-100",text:"text-blue-600",button:"bg-blue-600 hover:bg-blue-700",profileBg:"bg-blue-200",profileText:"text-blue-800"},{bg:"from-green-50 to-green-100",text:"text-green-600",button:"bg-green-600 hover:bg-green-700",profileBg:"bg-green-200",profileText:"text-green-800"},{bg:"from-purple-50 to-purple-100",text:"text-purple-600",button:"bg-purple-600 hover:bg-purple-700",profileBg:"bg-purple-200",profileText:"text-purple-800"},{bg:"from-orange-50 to-orange-100",text:"text-orange-600",button:"bg-orange-600 hover:bg-orange-700",profileBg:"bg-orange-200",profileText:"text-orange-800"}];(0,l.useEffect)(()=>{let e=setInterval(()=>{o(e=>(e+1)%s.length)},3e3);return()=>clearInterval(e)},[s.length]);let i=s[r];return(0,a.jsxs)("footer",{className:"relative py-12 border-t border-gray-100 overflow-hidden shadow-sm bg-white",children:[(0,a.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br ".concat(i.bg," opacity-20")}),(0,a.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-3"}),(0,a.jsx)("div",{className:"absolute -right-20 -bottom-20 w-64 h-64 rounded-full ".concat(i.button," opacity-10")}),(0,a.jsx)("div",{className:"absolute -left-20 -top-20 w-64 h-64 rounded-full ".concat(i.button," opacity-10")}),(0,a.jsx)("div",{className:"absolute inset-0 bg-noise opacity-[0.02]"})]}),(0,a.jsx)("div",{className:"absolute top-0 left-0 right-0 h-1 ".concat(i.button," z-10 transition-colors duration-500")}),(0,a.jsx)("div",{className:"absolute top-12 right-12 w-24 h-24 rounded-full ".concat(i.button," opacity-5 blur-xl")}),(0,a.jsx)("div",{className:"absolute bottom-12 left-12 w-32 h-32 rounded-full ".concat(i.button," opacity-5 blur-xl")}),(0,a.jsxs)("div",{className:"container mx-auto px-4 relative z-20",children:[(0,a.jsxs)("div",{className:"relative h-0.5 w-full mb-10 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 ".concat(i.button," opacity-70")}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent w-1/3 animate-shimmer"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsx)("h3",{className:"text-xl font-bold ".concat(i.text," mb-4 drop-shadow-sm"),children:"Ankush Gupta"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:"ML Engineer & Full Stack Developer specializing in building exceptional digital experiences."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("a",{href:"https://github.com/AnkushGitRepo",target:"_blank",rel:"noopener noreferrer",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),"aria-label":"GitHub",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})})}),(0,a.jsx)("a",{href:"https://linkedin.com/in/ankushgupta18",target:"_blank",rel:"noopener noreferrer",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),"aria-label":"LinkedIn",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"})})}),(0,a.jsx)("a",{href:"https://instagram.com/_ankushg",target:"_blank",rel:"noopener noreferrer",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),"aria-label":"Instagram",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium","aria-label":"Email",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})})]})]}),(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsx)("h3",{className:"text-xl font-bold ".concat(i.text," mb-4 drop-shadow-sm"),children:"Main Pages"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),children:"Home"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/about",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),children:"About Me"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/projects",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),children:"Projects"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/skills",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),children:"Skills"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/contact",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),children:"Contact Me"})})]})]}),(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsx)("h3",{className:"text-xl font-bold ".concat(i.text," mb-4 drop-shadow-sm"),children:"Resources"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/books",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),children:"Books"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/certifications",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),children:"Certifications"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/resume.pdf",target:"_blank",rel:"noopener noreferrer",className:"text-gray-700 hover:".concat(i.text," transition-colors duration-300 font-medium"),children:"Resume"})})]})]}),(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsx)("h3",{className:"text-xl font-bold ".concat(i.text," mb-4 drop-shadow-sm"),children:"Contact"}),(0,a.jsxs)("p",{className:"text-gray-700 mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Email:"})," <EMAIL>"]}),(0,a.jsxs)("p",{className:"text-gray-700 mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Location:"})," Ahmedabad, Gujarat, India"]}),(0,a.jsxs)("p",{className:"text-gray-700 mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Phone:"})," +91 7202906881"]})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-200 mt-8 pt-8 text-center relative",children:[(0,a.jsx)("div",{className:"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"}),(0,a.jsx)("div",{className:"w-20 h-1 ".concat(i.button," mx-auto mb-6")}),(0,a.jsxs)("p",{className:"text-gray-700 font-medium",children:["\xa9 ",e," ",(0,a.jsx)("span",{className:i.text,children:"Ankush Gupta"}),". All rights reserved."]})]})]})]})},x=e=>{let{children:t}=e,r="/"===(0,o.usePathname)();return(0,a.jsxs)("div",{className:"flex flex-col min-h-screen",children:[!r&&(0,a.jsx)(i,{}),(0,a.jsx)("main",{className:"flex-grow ".concat(r?"":"pt-16"),children:t}),(0,a.jsx)(d,{})]})}}}]);