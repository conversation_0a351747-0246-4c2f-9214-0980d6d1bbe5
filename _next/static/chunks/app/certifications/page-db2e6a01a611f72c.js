(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[736],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>a});var l=r(5155);function a(e){let{className:t="",children:r,variant:a="default",asChild:s=!1,onClick:n}=e,i="".concat("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2"," ").concat({default:"bg-blue-600 text-white hover:bg-blue-700 shadow-sm",outline:"border border-gray-200 hover:bg-gray-100 text-gray-900 shadow-sm",ghost:"hover:bg-gray-100 text-gray-900"}[a]," ").concat(t);return s?(0,l.jsx)("div",{className:i,onClick:n,children:r}):(0,l.jsx)("button",{className:i,onClick:n,children:r})}r(2115)},2931:(e,t,r)=>{Promise.resolve().then(r.bind(r,3840)),Promise.resolve().then(r.bind(r,3725)),Promise.resolve().then(r.bind(r,1359))},3786:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});let l=(0,r(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},3840:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var l=r(5155),a=r(2115),s=r(1359),n=r(9946);let i=(0,n.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var o=r(4416),c=r(3786),d=r(9434),g=r(6766),m=r(6126);function u(e){let{certification:t,onClose:r}=e,{}=(0,s.S)(),[n,u]=(0,a.useState)(0),[x,h]=(0,a.useState)(!1),b=[{bg:"from-blue-50 to-blue-100",text:"text-blue-600",button:"bg-blue-600 hover:bg-blue-700",profileBg:"bg-blue-200",profileText:"text-blue-800",border:"border-blue-200"},{bg:"from-green-50 to-green-100",text:"text-green-600",button:"bg-green-600 hover:bg-green-700",profileBg:"bg-green-200",profileText:"text-green-800",border:"border-green-200"},{bg:"from-purple-50 to-purple-100",text:"text-purple-600",button:"bg-purple-600 hover:bg-purple-700",profileBg:"bg-purple-200",profileText:"text-purple-800",border:"border-purple-200"},{bg:"from-orange-50 to-orange-100",text:"text-orange-600",button:"bg-orange-600 hover:bg-orange-700",profileBg:"bg-orange-200",profileText:"text-orange-800",border:"border-orange-200"}];(0,a.useEffect)(()=>{let e=setInterval(()=>{u(e=>(e+1)%b.length)},3e3);return()=>clearInterval(e)},[b.length]);let p=b[n],f=(0,a.useCallback)(()=>{h(!0),setTimeout(()=>{r(),h(!1)},300)},[r]);(0,a.useEffect)(()=>{let e=e=>{"Escape"===e.key&&f()};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[f]),(0,a.useEffect)(()=>(document.body.style.overflow="hidden",()=>{document.body.style.overflow="auto"}),[]);let v=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long"});return(0,l.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 backdrop-blur-sm transition-all duration-300 ".concat(p.text.replace("text-","bg-"),"/10"),onClick:f,children:(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-hidden transition-all duration-300 ".concat(x?"opacity-0 scale-95":"opacity-100 scale-100"),onClick:e=>e.stopPropagation(),children:[(0,l.jsx)("div",{className:"p-6 bg-gradient-to-r ".concat(p.bg),children:(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-12 h-12 rounded-full ".concat(p.profileBg," flex items-center justify-center mr-4"),children:(0,l.jsx)(i,{className:p.text,size:24})}),(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:t.title})]}),(0,l.jsx)("button",{onClick:f,className:"text-gray-500 hover:text-gray-700 transition-colors","aria-label":"Close modal",children:(0,l.jsx)(o.A,{size:24})})]})}),(0,l.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[(0,l.jsxs)("div",{className:"mb-6 relative h-64 md:h-80 w-full bg-gray-100 rounded-lg overflow-hidden",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,l.jsx)("div",{className:"w-10 h-10 border-3 border-gray-200 border-t-gray-600 rounded-full animate-spin"})}),(0,l.jsx)(g.default,{src:(0,d.O)(t.image),alt:t.title,fill:!0,className:"absolute inset-0 object-contain p-2 transition-opacity duration-500",onLoad:e=>{e.target.style.opacity="1",console.log("Modal image loaded: ".concat(t.title))},onError:()=>{console.log("Modal image error: ".concat(t.title))},style:{opacity:0}})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,l.jsxs)("div",{className:"p-4 rounded-lg bg-gray-50",children:[(0,l.jsx)("h3",{className:"".concat(p.text," font-medium mb-2"),children:"Issuer"}),(0,l.jsx)("p",{className:"text-gray-800",children:t.issuer})]}),(0,l.jsxs)("div",{className:"p-4 rounded-lg bg-gray-50",children:[(0,l.jsx)("h3",{className:"".concat(p.text," font-medium mb-2"),children:"Date"}),(0,l.jsxs)("p",{className:"text-gray-800",children:[v(t.issueDate),t.expirationDate&&(0,l.jsxs)("span",{className:"text-gray-500",children:[" - ",v(t.expirationDate)]})]})]})]}),(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h3",{className:"".concat(p.text," text-lg font-medium mb-3"),children:"Description"}),(0,l.jsx)("div",{className:"text-gray-700 space-y-4",children:t.description.split("\n\n").map((e,t)=>(0,l.jsx)("p",{children:e},t))})]}),t.skills&&t.skills.length>0&&(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h3",{className:"".concat(p.text," text-lg font-medium mb-3"),children:"Skills"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:t.skills.map((e,t)=>(0,l.jsx)(m.E,{className:"".concat(p.profileBg," ").concat(p.profileText," border-none"),children:e},t))})]})]}),(0,l.jsxs)("div",{className:"p-4 border-t border-gray-100 flex justify-between items-center",children:[(0,l.jsx)("div",{className:"flex gap-2",children:t.credentialUrl&&(0,l.jsxs)("a",{href:t.credentialUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-4 py-2 rounded-md ".concat(p.button," text-white font-medium hover:opacity-90 transition-all duration-300 shadow-sm hover:shadow transform hover:scale-105"),"aria-label":"Verify this certificate",children:[(0,l.jsx)(c.A,{size:18}),(0,l.jsx)("span",{children:"Verify Certificate"})]})}),(0,l.jsx)("button",{onClick:f,className:"px-4 py-2 rounded-md text-gray-600 hover:bg-gray-100 transition-colors",children:"Close"})]})]})})}var x=r(6932);let h=(0,n.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var b=r(285);let p={Languages:["Python","JavaScript","Java","HTML","CSS"],Skills:["Web Development","Data Structures","Object-Oriented Programming","Programming","Software Development","Front-end Development","Ingenious Hackathon 6.0"],Tools:["GitHub","Version Control","Git","Repository Management"],Workshop:["LinkedIn Workshop","2 Day AI Mastery Workshop","Personal Branding","Professional Networking","Content Strategy"],"AI/ML":["Machine Learning","Artificial Intelligence","Neural Networks","AI Applications","Generative AI","Prompt Engineering","AI Integration","Regression","Classification","Model Evaluation"],Hackathon:["Hackathon","Ingenious Hackathon 6.0","Innovation","Teamwork"],All:[]};function f(e){let{certifications:t}=e,{currentColor:r}=(0,s.S)(),[n,i]=(0,a.useState)(null),[f,v]=(0,a.useState)(0),[y,j]=(0,a.useState)(!0),[N,w]=(0,a.useState)(["All"]);(0,a.useEffect)(()=>{let e=setTimeout(()=>{j(!1),console.log("Certificate image paths:"),t.forEach(e=>{console.log("".concat(e.title,": ").concat(e.image))})},1e3);return()=>clearTimeout(e)},[t]);let k=e=>{if("All"===e)return void w(["All"]);if(N.includes("All"))return void w([e]);if(N.includes(e)){let t=N.filter(t=>t!==e);w(0===t.length?["All"]:t)}else w([...N,e])},A=[{bg:"from-blue-50 to-blue-100",text:"text-blue-600",button:"bg-blue-600 hover:bg-blue-700",profileBg:"bg-blue-200",profileText:"text-blue-800",border:"border-blue-200",shadow:"shadow-blue-100",hover:"hover:bg-blue-50"},{bg:"from-green-50 to-green-100",text:"text-green-600",button:"bg-green-600 hover:bg-green-700",profileBg:"bg-green-200",profileText:"text-green-800",border:"border-green-200",shadow:"shadow-green-100",hover:"hover:bg-green-50"},{bg:"from-purple-50 to-purple-100",text:"text-purple-600",button:"bg-purple-600 hover:bg-purple-700",profileBg:"bg-purple-200",profileText:"text-purple-800",border:"border-purple-200",shadow:"shadow-purple-100",hover:"hover:bg-purple-50"},{bg:"from-orange-50 to-orange-100",text:"text-orange-600",button:"bg-orange-600 hover:bg-orange-700",profileBg:"bg-orange-200",profileText:"text-orange-800",border:"border-orange-200",shadow:"shadow-orange-100",hover:"hover:bg-orange-50"}];(0,a.useEffect)(()=>{let e=setInterval(()=>{v(e=>(e+1)%A.length)},3e3);return()=>clearInterval(e)},[A.length]);let C=A[f],E=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long"}),S=(0,a.useMemo)(()=>{if(N.includes("All"))return t;let e=N.flatMap(e=>p[e]);return t.filter(t=>(!N.includes("Hackathon")||"2 Day AI Mastery Workshop"!==t.title)&&!!t.skills&&0!==t.skills.length&&t.skills.some(r=>e.includes(r)?!(N.includes("Languages")&&1===N.length&&"Supervised Machine Learning: Regression and Classification"===t.title||N.includes("Hackathon")&&"2 Day AI Mastery Workshop"===t.title):!!(N.includes("Workshop")&&(t.title.toLowerCase().includes("workshop")||e.some(e=>t.title.includes(e)))||N.includes("Hackathon")&&t.title.toLowerCase().includes("hackathon")||N.includes("Skills")&&"Ingenious Hackathon 6.0"===t.title||N.includes("Tools")&&"GitHub Foundations"===t.title)))},[t,N]);return(0,l.jsx)("section",{className:"py-20 mt-0 bg-gradient-to-b ".concat(C.bg," transition-colors duration-500 px-4 sm:px-6 lg:px-8"),children:(0,l.jsxs)("div",{className:"container mx-auto",children:[(0,l.jsxs)("div",{className:"mb-8 text-center",children:[(0,l.jsx)("h1",{className:"text-4xl font-bold ".concat(C.text," mb-4"),children:"Certifications"}),(0,l.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"A collection of my professional certifications and credentials in machine learning, web development, and cloud technologies."})]}),(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,l.jsx)(x.A,{size:18,className:"mr-2 text-gray-600"}),(0,l.jsx)("h2",{className:"text-lg font-medium text-gray-700",children:"Filter by Category"})]}),(0,l.jsx)("div",{className:"flex flex-wrap justify-center gap-2 mb-2",children:["All",...Object.keys(p).filter(e=>"All"!==e)].map(e=>(0,l.jsxs)(b.$,{variant:N.includes(e)?"default":"outline",className:"rounded-full text-sm py-1 px-3 ".concat(N.includes(e)?C.button:"hover:bg-gray-100"),onClick:()=>k(e),children:[e,N.includes(e)&&"All"!==e&&(0,l.jsx)(o.A,{size:14,className:"ml-1"})]},e))}),!N.includes("All")&&(0,l.jsx)("div",{className:"text-center",children:(0,l.jsx)("button",{className:"text-sm ".concat(C.text," hover:underline"),onClick:()=>w(["All"]),children:"Clear filters"})})]}),y?(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,l.jsxs)("div",{className:"rounded-lg overflow-hidden border border-gray-200 shadow-sm animate-pulse",children:[(0,l.jsx)("div",{className:"h-48 bg-gray-200"}),(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mb-3"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/3 mb-4"}),(0,l.jsxs)("div",{className:"flex gap-2 mb-4",children:[(0,l.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16"}),(0,l.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16"})]}),(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mt-2"})]})]},t))}):(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:0===S.length?(0,l.jsxs)("div",{className:"col-span-3 text-center py-12",children:[(0,l.jsx)("div",{className:"text-gray-500 mb-4",children:"No certifications match the selected filters"}),(0,l.jsx)(b.$,{variant:"outline",className:"".concat(C.text),onClick:()=>w(["All"]),children:"Show All Certifications"})]}):S.map(e=>(0,l.jsxs)("div",{className:"rounded-lg overflow-hidden border ".concat(C.border," ").concat(C.shadow," transition-all duration-300 ").concat(C.hover," cursor-pointer flex flex-col h-full"),onClick:()=>i(e),children:[(0,l.jsxs)("div",{className:"relative h-48 w-full overflow-hidden bg-white",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-50",children:(0,l.jsx)("div",{className:"w-8 h-8 border-2 border-gray-200 border-t-gray-600 rounded-full animate-spin"})}),(0,l.jsx)(g.default,{src:(0,d.O)(e.image),alt:"".concat(e.title," Certificate"),fill:!0,className:"absolute inset-0 object-contain p-2 transition-opacity duration-300",onLoad:t=>{t.target.style.opacity="1",console.log("Image loaded: ".concat(e.title))},onError:()=>{console.log("Image error: ".concat(e.title))},style:{opacity:0}})]}),(0,l.jsxs)("div",{className:"p-4 flex-1 flex flex-col justify-between",children:[(0,l.jsxs)("div",{className:"mb-auto",children:[(0,l.jsx)("h3",{className:"text-lg font-bold text-gray-800 mb-2 line-clamp-2",children:e.title}),(0,l.jsxs)("div",{className:"flex items-center text-gray-500 mb-2 text-sm",children:[(0,l.jsx)("span",{className:"font-medium mr-1",children:"Issuer:"}),(0,l.jsx)("span",{className:"line-clamp-1",children:e.issuer})]}),(0,l.jsxs)("div",{className:"flex items-center text-gray-500 mb-3 text-sm",children:[(0,l.jsx)(h,{size:14,className:"mr-1 flex-shrink-0"}),(0,l.jsx)("span",{children:E(e.issueDate)}),e.expirationDate&&(0,l.jsxs)("span",{className:"ml-1",children:["- ",E(e.expirationDate)]})]})]}),e.skills&&e.skills.length>0&&(0,l.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2 mb-3",children:[e.skills.slice(0,3).map((e,t)=>(0,l.jsx)(m.E,{className:"".concat(C.profileBg," ").concat(C.profileText," border-none text-xs"),children:e},t)),e.skills.length>3&&(0,l.jsxs)(m.E,{className:"bg-gray-100 text-gray-600 border-none text-xs",children:["+",e.skills.length-3]})]}),(0,l.jsxs)("div",{className:"mt-2 sm:mt-3 flex flex-col gap-2",children:[(0,l.jsxs)("button",{className:"inline-flex items-center justify-center ".concat(C.text," font-medium text-sm py-2 px-4 rounded hover:bg-gray-50 active:bg-gray-100 transition-colors w-full border border-gray-200 touch-manipulation"),onClick:t=>{t.stopPropagation(),i(e)},"aria-label":"View certificate details",children:[(0,l.jsx)("span",{children:"View Details"})," ",(0,l.jsx)(c.A,{size:14,className:"ml-1 flex-shrink-0"})]}),e.credentialUrl&&(0,l.jsxs)("a",{href:e.credentialUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center ".concat(C.button," text-white font-medium text-sm py-2 px-4 rounded transition-all duration-300 w-full touch-manipulation"),onClick:e=>e.stopPropagation(),"aria-label":"Verify this certificate",children:[(0,l.jsx)("span",{children:"Verify Certificate"})," ",(0,l.jsx)(c.A,{size:14,className:"ml-1 flex-shrink-0"})]})]})]})]},e._id))}),n&&(0,l.jsx)(u,{certification:n,onClose:()=>i(null)})]})})}},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});let l=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var l=r(5155);function a(e){let{className:t="",children:r,variant:a="default"}=e;return(0,l.jsx)("span",{className:"".concat("inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"," ").concat({default:"bg-blue-100 text-blue-800",outline:"border border-gray-200 text-gray-700 shadow-sm"}[a]," ").concat(t),children:r})}r(2115)},6932:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});let l=(0,r(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},9434:(e,t,r)=>{"use strict";function l(e){return e.startsWith("http")||e.startsWith("/Portfolio")?e:"/Portfolio".concat(e)}r.d(t,{O:()=>l})},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var l=r(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,l.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:d="",children:g,iconNode:m,...u}=e;return(0,l.createElement)("svg",{ref:t,...c,width:a,height:a,stroke:r,strokeWidth:n?24*Number(s)/Number(a):s,className:i("lucide",d),...!g&&!o(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,r]=e;return(0,l.createElement)(t,r)}),...Array.isArray(g)?g:[g]])}),g=(e,t)=>{let r=(0,l.forwardRef)((r,s)=>{let{className:o,...c}=r;return(0,l.createElement)(d,{ref:s,iconNode:t,className:i("lucide-".concat(a(n(e))),"lucide-".concat(e),o),...c})});return r.displayName=n(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[244,766,725,441,684,358],()=>t(2931)),_N_E=e.O()}]);