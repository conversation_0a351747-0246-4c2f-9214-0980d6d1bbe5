(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{551:(e,t,a)=>{Promise.resolve().then(a.bind(a,3725)),Promise.resolve().then(a.bind(a,7602))},1e3:(e,t,a)=>{"use strict";a.d(t,{gp:()=>i,RS:()=>c});let r="http://localhost:5001/api";async function n(){let e=await fetch("".concat(r,"/skills"));if(!e.ok)throw Error("Failed to fetch skills");return e.json()}async function s(e){let t=await fetch("".concat(r,"/contact"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to submit contact form");return t.json()}var o=a(5055);let l=window.location.hostname.includes("github.io");async function i(){if(l)return o.HQ;try{return await n()}catch(e){return console.warn("Failed to fetch skills from API, using mock data",e),o.HQ}}async function c(e){if(l)return(0,o.v0)();try{return await s(e)}catch(e){return console.warn("Failed to submit contact form to API, using mock handler",e),(0,o.v0)()}}},7602:(e,t,a)=>{"use strict";a.d(t,{default:()=>g});var r=a(5155),n=a(2115),s=a(1e3),o=a(3717),l=a(1359),i=a(9099),c=a(2894),d=a(5684),m=a(6474),u=a(1934);let g=()=>{let[e,t]=(0,n.useState)(0),{currentColor:a}=(0,l.S)(),[g,h]=(0,n.useState)({name:"",email:"",contactReason:o.z.JOB_OPPORTUNITY,message:""}),[b,x]=(0,n.useState)(!1),[p,f]=(0,n.useState)(null),y=e=>{let{name:t,value:a}=e.target;h(e=>({...e,[t]:a}))},w=async e=>{e.preventDefault(),x(!0),f(null);try{let e=await (0,s.RS)(g);f({success:!0,message:e.message||"Your message has been sent successfully!"}),h({name:"",email:"",contactReason:o.z.JOB_OPPORTUNITY,message:""})}catch(e){f({success:!1,message:"Failed to send message. Please try again later."}),console.error(e)}finally{x(!1)}},v=[{bg:"from-blue-50 to-blue-100",solidBg:"bg-blue-50",leftBg:"bg-blue-100",text:"text-blue-600",button:"bg-blue-600 hover:bg-blue-700",border:"border-blue-200",hoverText:"hover:text-blue-600"},{bg:"from-green-50 to-green-100",solidBg:"bg-green-50",leftBg:"bg-green-100",text:"text-green-600",button:"bg-green-600 hover:bg-green-700",border:"border-green-200",hoverText:"hover:text-green-600"},{bg:"from-purple-50 to-purple-100",solidBg:"bg-purple-50",leftBg:"bg-purple-100",text:"text-purple-600",button:"bg-purple-600 hover:bg-purple-700",border:"border-purple-200",hoverText:"hover:text-purple-600"},{bg:"from-orange-50 to-orange-100",solidBg:"bg-orange-50",leftBg:"bg-orange-100",text:"text-orange-600",button:"bg-orange-600 hover:bg-orange-700",border:"border-orange-200",hoverText:"hover:text-orange-600"}];(0,n.useEffect)(()=>{let e=setInterval(()=>{t(e=>(e+1)%v.length)},3e3);return()=>clearInterval(e)},[v.length]);let j=v[e];return(0,r.jsx)("section",{id:"contact",className:"min-h-screen w-full flex items-center justify-center ".concat(j.solidBg," py-8 px-2 sm:px-4 transition-colors duration-500"),children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row w-full max-w-6xl min-h-[70vh] bg-white/70 rounded-2xl shadow-xl overflow-hidden border border-gray-100",children:[(0,r.jsxs)(u.P.div,{initial:{x:-50,opacity:0},animate:{x:0,opacity:1},transition:{duration:.7},className:"w-full lg:w-1/2 flex flex-col justify-center items-start px-6 sm:px-10 py-10 lg:py-16 border-b lg:border-b-0 lg:border-r ".concat(j.border," ").concat(j.leftBg," transition-colors duration-500"),children:[(0,r.jsxs)("h2",{className:"font-extrabold leading-tight mb-6 text-left ".concat(j.text," relative"),style:{fontSize:"clamp(2rem,6vw,3.5rem)"},children:[(0,r.jsx)("span",{className:"block mb-2",children:"Let's Create"}),(0,r.jsx)("span",{className:"block",children:"Something Amazing"}),(0,r.jsx)("span",{className:"absolute -bottom-4 left-0 w-24 h-1 bg-gradient-to-r from-current to-transparent opacity-50"})]}),(0,r.jsxs)("p",{className:"text-base mb-2 text-gray-900 mt-8 max-w-xs",children:["Contact me at"," ",(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"".concat(j.text," font-medium hover:underline transition-colors"),children:"<EMAIL>"})]}),(0,r.jsx)("div",{className:"mb-2 mt-8",style:{fontSize:"2rem",fontWeight:700,color:"#bdbdbd",lineHeight:1},children:"OR"}),(0,r.jsx)("p",{className:"text-base mb-8 text-gray-900 max-w-xs",children:"Fill in the form and I will reach out to you shortly."}),(0,r.jsxs)("div",{className:"flex space-x-4 mt-4",children:[(0,r.jsx)(u.P.a,{href:"https://github.com/AnkushGitRepo",target:"_blank",rel:"noopener noreferrer",whileHover:{scale:1.15},className:"".concat(j.text," transition-colors"),"aria-label":"GitHub",children:(0,r.jsx)(i.A,{className:"h-6 w-6 lg:h-7 lg:w-7"})}),(0,r.jsx)(u.P.a,{href:"https://linkedin.com/in/ankushgupta18",target:"_blank",rel:"noopener noreferrer",whileHover:{scale:1.15},className:"".concat(j.text," transition-colors"),"aria-label":"LinkedIn",children:(0,r.jsx)(c.A,{className:"h-6 w-6 lg:h-7 lg:w-7"})}),(0,r.jsx)(u.P.a,{href:"https://instagram.com/_ankushg",target:"_blank",rel:"noopener noreferrer",whileHover:{scale:1.15},className:"".concat(j.text," transition-colors"),"aria-label":"Instagram",children:(0,r.jsx)(d.A,{className:"h-6 w-6 lg:h-7 lg:w-7"})})]})]}),(0,r.jsx)(u.P.div,{initial:{x:50,opacity:0},animate:{x:0,opacity:1},transition:{duration:.7,delay:.2},className:"w-full lg:w-1/2 flex flex-col justify-center items-center px-6 sm:px-10 py-10 lg:py-16 bg-white h-auto m-0 transition-colors duration-500",children:(0,r.jsxs)("form",{onSubmit:w,className:"w-full max-w-md mx-auto space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium mb-1 text-gray-900",children:"Name"}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:g.name,onChange:y,required:!0,className:"w-full px-4 py-3 border ".concat(j.border," rounded-lg focus:ring-2 focus:ring-").concat(a,"-500 focus:border-transparent bg-white text-gray-900 transition-all duration-300"),placeholder:"Enter Name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium mb-1 text-gray-900",children:"Email"}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:g.email,onChange:y,required:!0,className:"w-full px-4 py-3 border ".concat(j.border," rounded-lg focus:ring-2 focus:ring-").concat(a,"-500 focus:border-transparent bg-white text-gray-900 transition-all duration-300"),placeholder:"Enter Email"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("label",{htmlFor:"contactReason",className:"block text-sm font-medium mb-1 text-gray-900",children:"Reason to Connect"}),(0,r.jsx)("select",{id:"contactReason",name:"contactReason",value:g.contactReason,onChange:e=>{let t=e.target.value;h(e=>({...e,contactReason:t}))},required:!0,className:"w-full px-4 py-3 border ".concat(j.border," rounded-lg focus:ring-2 focus:ring-").concat(a,"-500 focus:border-transparent bg-white text-gray-900 transition-all duration-300 appearance-none pr-10"),style:{WebkitAppearance:"none",MozAppearance:"none",appearance:"none"},children:Object.values(o.z).map(e=>(0,r.jsx)("option",{value:e,children:e},e))}),(0,r.jsx)("div",{className:"pointer-events-none absolute right-4 top-1/2 -translate-y-1/2 flex items-center h-full",children:(0,r.jsx)(m.A,{className:"".concat(j.text," w-5 h-5")})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium mb-1 text-gray-900",children:"Message"}),(0,r.jsx)("textarea",{id:"message",name:"message",value:g.message,onChange:y,required:!0,rows:5,className:"w-full px-4 py-3 border ".concat(j.border," rounded-lg focus:ring-2 focus:ring-").concat(a,"-500 focus:border-transparent bg-white text-gray-900 transition-all duration-300"),placeholder:"Type your message here."})]}),p&&(0,r.jsx)("div",{className:"p-4 rounded-lg text-center ".concat(p.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"," transition-all duration-300 animate-fadeIn"),children:p.success?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"font-semibold text-lg mb-1",children:"Thank you for reaching out!"}),(0,r.jsx)("div",{children:"Your message has been received. I will get back to you as soon as possible."}),(0,r.jsx)("div",{className:"mt-2 text-sm text-green-700",children:"A confirmation email has been sent to your inbox."})]}):p.message}),(0,r.jsx)("button",{type:"submit",disabled:b,className:"w-full px-6 py-3 ".concat(j.button," text-white font-medium rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 shadow-md hover:shadow-lg text-center"),children:b?"Sending...":"SEND MESSAGE"})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[244,789,725,55,441,684,358],()=>t(551)),_N_E=e.O()}]);