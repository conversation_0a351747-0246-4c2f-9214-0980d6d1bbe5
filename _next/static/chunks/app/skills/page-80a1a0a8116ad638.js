(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[501],{2550:(e,t,a)=>{Promise.resolve().then(a.bind(a,3725)),Promise.resolve().then(a.bind(a,5030)),Promise.resolve().then(a.bind(a,1359))},5030:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var l=a(5155),r=a(2115),n=a(3717),s=a(2907),i=a(1359),c=a(9434);let o=()=>{let[e,t]=(0,r.useState)([]),[a,o]=(0,r.useState)("all"),[d,m]=(0,r.useState)(!0),[g,x]=(0,r.useState)(null),{currentColor:h}=(0,i.S)(),[u,b]=(0,r.useState)(0),p=[{bg:"from-blue-50 to-blue-100",accent:"bg-blue-600",text:"text-blue-600",light:"bg-blue-100"},{bg:"from-green-50 to-green-100",accent:"bg-green-600",text:"text-green-600",light:"bg-green-100"},{bg:"from-purple-50 to-purple-100",accent:"bg-purple-600",text:"text-purple-600",light:"bg-purple-100"},{bg:"from-orange-50 to-orange-100",accent:"bg-orange-600",text:"text-orange-600",light:"bg-orange-100"}];(0,r.useEffect)(()=>{let e=setInterval(()=>{b(e=>(e+1)%p.length)},3e3);return()=>clearInterval(e)},[p.length]);let f=p[u];(0,r.useEffect)(()=>{(async()=>{try{let e=await (0,s.gp)();t(e)}catch(e){x("Failed to load skills. Please try again later."),console.error(e)}finally{m(!1)}})()},[]);let v=[{id:"all",name:"All"},...Object.values(n.U).map(e=>({id:e,name:e}))],j=e.reduce((e,t)=>{let a=t.category;return e[a]||(e[a]=[]),e[a].push(t),e},{}),y="all"===a?e:e.filter(e=>e.category===a);return(0,l.jsxs)("section",{className:"py-20 relative",id:"skills",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br ".concat(f.bg," transition-all duration-500")}),(0,l.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsx)("div",{className:"inline-block px-3 py-1 rounded-full text-sm font-medium mb-4 transition-colors duration-500 ".concat(f.light," ").concat(f.text),children:"My Expertise"}),(0,l.jsx)("h2",{className:"text-3xl md:text-5xl font-bold tracking-tight text-center mb-4",children:(0,l.jsx)("span",{className:"bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent",children:"Skills & Technologies"})}),(0,l.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto mt-4",children:"Here are the technologies and tools I work with."}),(0,l.jsx)("div",{className:"w-20 h-1 rounded-full mx-auto mt-4 transition-colors duration-500 ".concat(f.accent)})]}),(0,l.jsx)("div",{className:"flex flex-wrap justify-center gap-3 mb-12",children:v.map(e=>(0,l.jsx)("button",{onClick:()=>o(e.id),className:"px-4 py-2 rounded-full transition-colors duration-300 ".concat(a===e.id?"".concat(f.accent," text-white shadow-md"):"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:e.name},e.id))}),d?(0,l.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 ".concat(f.accent)})}):g?(0,l.jsx)("div",{className:"text-center text-red-600 py-8",children:g}):"all"!==a?(0,l.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6",children:y.map((e,t)=>(0,l.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm p-6 rounded-lg shadow-md flex flex-col items-center transition-all duration-300 hover:shadow-lg hover:transform hover:scale-105 opacity-0 animate-fadeIn",style:{animationDelay:"".concat(50*t,"ms"),animationFillMode:"forwards"},children:[(0,l.jsx)("div",{className:"w-16 h-16 mb-4 flex items-center justify-center ".concat(f.light," rounded-lg"),children:(0,l.jsx)("div",{style:{backgroundImage:"url(".concat((0,c.O)(e.icon),")"),backgroundSize:"contain",backgroundPosition:"center",backgroundRepeat:"no-repeat",width:"36px",height:"36px"},"aria-label":e.name,role:"img"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 text-center",children:e.name})]},e._id))}):(0,l.jsx)("div",{className:"space-y-12",children:Object.entries(j).map(e=>{let[t,a]=e;return(0,l.jsxs)("div",{className:"mb-10",children:[(0,l.jsx)("h3",{className:"text-2xl font-bold mb-6 ".concat(f.text," border-b pb-2"),children:t}),(0,l.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6",children:a.map((e,t)=>(0,l.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm p-6 rounded-lg shadow-md flex flex-col items-center transition-all duration-300 hover:shadow-lg hover:transform hover:scale-105 opacity-0 animate-fadeIn",style:{animationDelay:"".concat(50*t,"ms"),animationFillMode:"forwards"},children:[(0,l.jsx)("div",{className:"w-16 h-16 mb-4 flex items-center justify-center ".concat(f.light," rounded-lg"),children:(0,l.jsx)("div",{style:{backgroundImage:"url(".concat((0,c.O)(e.icon),")"),backgroundSize:"contain",backgroundPosition:"center",backgroundRepeat:"no-repeat",width:"36px",height:"36px"},"aria-label":e.name,role:"img"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 text-center",children:e.name})]},e._id))})]},t)})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[244,725,55,907,441,684,358],()=>t(2550)),_N_E=e.O()}]);