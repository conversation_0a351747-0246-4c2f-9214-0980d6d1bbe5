(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[501],{1e3:(e,t,a)=>{"use strict";a.d(t,{gp:()=>o,RS:()=>c});let n="http://localhost:5001/api";async function l(){let e=await fetch("".concat(n,"/skills"));if(!e.ok)throw Error("Failed to fetch skills");return e.json()}async function r(e){let t=await fetch("".concat(n,"/contact"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to submit contact form");return t.json()}var s=a(5055);let i=window.location.hostname.includes("github.io");async function o(){if(i)return s.HQ;try{return await l()}catch(e){return console.warn("Failed to fetch skills from API, using mock data",e),s.HQ}}async function c(e){if(i)return(0,s.v0)();try{return await r(e)}catch(e){return console.warn("Failed to submit contact form to API, using mock handler",e),(0,s.v0)()}}},2550:(e,t,a)=>{Promise.resolve().then(a.bind(a,3725)),Promise.resolve().then(a.bind(a,5030)),Promise.resolve().then(a.bind(a,1359))},5030:(e,t,a)=>{"use strict";a.d(t,{default:()=>c});var n=a(5155),l=a(2115),r=a(3717),s=a(1e3),i=a(1359),o=a(9434);let c=()=>{let[e,t]=(0,l.useState)([]),[a,c]=(0,l.useState)("all"),[d,m]=(0,l.useState)(!0),[g,h]=(0,l.useState)(null),{currentColor:u}=(0,i.S)(),[x,b]=(0,l.useState)(0),p=[{bg:"from-blue-50 to-blue-100",accent:"bg-blue-600",text:"text-blue-600",light:"bg-blue-100"},{bg:"from-green-50 to-green-100",accent:"bg-green-600",text:"text-green-600",light:"bg-green-100"},{bg:"from-purple-50 to-purple-100",accent:"bg-purple-600",text:"text-purple-600",light:"bg-purple-100"},{bg:"from-orange-50 to-orange-100",accent:"bg-orange-600",text:"text-orange-600",light:"bg-orange-100"}];(0,l.useEffect)(()=>{let e=setInterval(()=>{b(e=>(e+1)%p.length)},3e3);return()=>clearInterval(e)},[p.length]);let f=p[x];(0,l.useEffect)(()=>{(async()=>{try{let e=await (0,s.gp)();t(e)}catch(e){h("Failed to load skills. Please try again later."),console.error(e)}finally{m(!1)}})()},[]);let v=[{id:"all",name:"All"},...Object.values(r.U).map(e=>({id:e,name:e}))],y=e.reduce((e,t)=>{let a=t.category;return e[a]||(e[a]=[]),e[a].push(t),e},{}),j="all"===a?e:e.filter(e=>e.category===a);return(0,n.jsxs)("section",{className:"py-20 relative",id:"skills",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br ".concat(f.bg," transition-all duration-500")}),(0,n.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,n.jsxs)("div",{className:"text-center mb-16",children:[(0,n.jsx)("div",{className:"inline-block px-3 py-1 rounded-full text-sm font-medium mb-4 transition-colors duration-500 ".concat(f.light," ").concat(f.text),children:"My Expertise"}),(0,n.jsx)("h2",{className:"text-3xl md:text-5xl font-bold tracking-tight text-center mb-4",children:(0,n.jsx)("span",{className:"bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent",children:"Skills & Technologies"})}),(0,n.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto mt-4",children:"Here are the technologies and tools I work with."}),(0,n.jsx)("div",{className:"w-20 h-1 rounded-full mx-auto mt-4 transition-colors duration-500 ".concat(f.accent)})]}),(0,n.jsx)("div",{className:"flex flex-wrap justify-center gap-3 mb-12",children:v.map(e=>(0,n.jsx)("button",{onClick:()=>c(e.id),className:"px-4 py-2 rounded-full transition-colors duration-300 ".concat(a===e.id?"".concat(f.accent," text-white shadow-md"):"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:e.name},e.id))}),d?(0,n.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 ".concat(f.accent)})}):g?(0,n.jsx)("div",{className:"text-center text-red-600 py-8",children:g}):"all"!==a?(0,n.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6",children:j.map((e,t)=>(0,n.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm p-6 rounded-lg shadow-md flex flex-col items-center transition-all duration-300 hover:shadow-lg hover:transform hover:scale-105 opacity-0 animate-fadeIn",style:{animationDelay:"".concat(50*t,"ms"),animationFillMode:"forwards"},children:[(0,n.jsx)("div",{className:"w-16 h-16 mb-4 flex items-center justify-center ".concat(f.light," rounded-lg"),children:(0,n.jsx)("div",{style:{backgroundImage:"url(".concat((0,o.O)(e.icon),")"),backgroundSize:"contain",backgroundPosition:"center",backgroundRepeat:"no-repeat",width:"36px",height:"36px"},"aria-label":e.name,role:"img"})}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 text-center",children:e.name})]},e._id))}):(0,n.jsx)("div",{className:"space-y-12",children:Object.entries(y).map(e=>{let[t,a]=e;return(0,n.jsxs)("div",{className:"mb-10",children:[(0,n.jsx)("h3",{className:"text-2xl font-bold mb-6 ".concat(f.text," border-b pb-2"),children:t}),(0,n.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6",children:a.map((e,t)=>(0,n.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm p-6 rounded-lg shadow-md flex flex-col items-center transition-all duration-300 hover:shadow-lg hover:transform hover:scale-105 opacity-0 animate-fadeIn",style:{animationDelay:"".concat(50*t,"ms"),animationFillMode:"forwards"},children:[(0,n.jsx)("div",{className:"w-16 h-16 mb-4 flex items-center justify-center ".concat(f.light," rounded-lg"),children:(0,n.jsx)("div",{style:{backgroundImage:"url(".concat((0,o.O)(e.icon),")"),backgroundSize:"contain",backgroundPosition:"center",backgroundRepeat:"no-repeat",width:"36px",height:"36px"},"aria-label":e.name,role:"img"})}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 text-center",children:e.name})]},e._id))})]},t)})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[244,725,55,441,684,358],()=>t(2550)),_N_E=e.O()}]);