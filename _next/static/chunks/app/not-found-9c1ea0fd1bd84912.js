(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{1944:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var n=r(5155),s=r(2115),i=r(5695),o=r(6874),a=r.n(o),l=r(6766),u=r(9434),c=r(9137),d=r.n(c);function h(e){let{currentColor:t}=e,r=[];for(let e=0;e<40;e++){let t=Math.floor(60*Math.random())+20,s="".concat(100*Math.random(),"%"),i="".concat(100*Math.random(),"%"),o=.2*Math.random()+.1,a=t/3;r.push((0,n.jsx)("div",{className:"absolute rounded-full bg-white pointer-events-none",style:{width:t,height:t,left:s,top:i,opacity:o,filter:"blur(".concat(a,"px)"),animation:"float ".concat(10*Math.random()+10,"s ease-in-out infinite"),animationDelay:"".concat(5*Math.random(),"s")}},e))}return(0,n.jsxs)("div",{className:"jsx-b0d38d4bc61faf9d "+"fixed inset-0 w-full h-full overflow-hidden bg-gradient-to-br ".concat((e=>{switch(e){case"blue":default:return"from-blue-50 to-blue-100";case"green":return"from-green-50 to-green-100";case"purple":return"from-purple-50 to-purple-100";case"orange":return"from-orange-50 to-orange-100"}})(t)," transition-colors duration-1000"),children:[r,(0,n.jsx)(d(),{id:"b0d38d4bc61faf9d",children:"@-webkit-keyframes float{0%{-webkit-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-20px);transform:translatey(-20px)}100%{-webkit-transform:translatey(0px);transform:translatey(0px)}}@-moz-keyframes float{0%{-moz-transform:translatey(0px);transform:translatey(0px)}50%{-moz-transform:translatey(-20px);transform:translatey(-20px)}100%{-moz-transform:translatey(0px);transform:translatey(0px)}}@-o-keyframes float{0%{-o-transform:translatey(0px);transform:translatey(0px)}50%{-o-transform:translatey(-20px);transform:translatey(-20px)}100%{-o-transform:translatey(0px);transform:translatey(0px)}}@keyframes float{0%{-webkit-transform:translatey(0px);-moz-transform:translatey(0px);-o-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-20px);-moz-transform:translatey(-20px);-o-transform:translatey(-20px);transform:translatey(-20px)}100%{-webkit-transform:translatey(0px);-moz-transform:translatey(0px);-o-transform:translatey(0px);transform:translatey(0px)}}"})]})}function f(){let e=(0,i.useRouter)(),[t,r]=(0,s.useState)(0),[o,c]=(0,s.useState)(!1),[d,f]=(0,s.useState)(4),p=[{text:"text-blue-600",button:"bg-blue-600 hover:bg-blue-700",buttonText:"text-white"},{text:"text-green-600",button:"bg-green-600 hover:bg-green-700",buttonText:"text-white"},{text:"text-purple-600",button:"bg-purple-600 hover:bg-purple-700",buttonText:"text-white"},{text:"text-orange-600",button:"bg-orange-600 hover:bg-orange-700",buttonText:"text-white"}];(0,s.useEffect)(()=>{let e=setInterval(()=>{r(e=>(e+1)%p.length)},3e3);return()=>clearInterval(e)},[p.length]),(0,s.useEffect)(()=>{if(d<=0)return void e.push("/");let t=setTimeout(()=>{f(d-1)},1e3);return()=>clearTimeout(t)},[d,e]);let m=p[t],y=["blue","green","purple","orange"][t];return(0,n.jsxs)("div",{className:"fixed inset-0 w-full h-full",children:[(0,n.jsx)(h,{currentColor:y}),(0,n.jsxs)("div",{className:"relative z-10 w-full h-full flex flex-col items-center justify-center",children:[(0,n.jsx)("div",{className:"mb-8 transition-transform duration-300 hover:scale-105",style:{maxWidth:"350px"},children:(0,n.jsx)(l.default,{src:(0,u.O)("/404.png"),alt:"404 Error",width:350,height:350,className:"w-full h-auto transition-opacity duration-1000 ".concat(o?"opacity-100":"opacity-0"),onLoad:()=>c(!0),priority:!0})}),(0,n.jsxs)("div",{className:"text-center px-4 max-w-md",children:[(0,n.jsx)("h2",{className:"text-2xl md:text-3xl font-bold mb-3 ".concat(m.text," transition-colors duration-1000"),children:"Page Not Found"}),(0,n.jsx)("p",{className:"text-gray-800 mb-6 text-lg",children:"Oops! The page you're looking for doesn't exist or has been moved."}),(0,n.jsx)(a(),{href:"/",className:"inline-block px-6 py-3 rounded-md ".concat(m.button," ").concat(m.buttonText," font-medium transition-all duration-500 transform hover:scale-105 hover:shadow-lg"),children:"Return Home"}),(0,n.jsxs)("p",{className:"mt-4 text-sm text-gray-600",children:["Redirecting to home page in ",d," second",1!==d?"s":"","..."]})]})]})]})}},2269:(e,t,r)=>{"use strict";var n=r(1890);r(8375);var s=r(2115),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),o=void 0!==n&&n.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,s=t.optimizeForSpeed,i=void 0===s?o:s;u(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",u("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){u("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(u(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(u(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];u(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];u(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&u(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return r?s.insertBefore(n,r):s.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function u(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return d[n]||(d[n]="jsx-"+c(e+"-"+r)),d[n]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,s=t.optimizeForSpeed,i=void 0!==s&&s;this._sheet=n||new l({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),n&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,s=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var i=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=i,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var s=h(n,r);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return f(s,e)}):[f(s,t)]}}return{styleId:h(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=s.createContext(null);m.displayName="StyleSheetContext";var y=i.default.useInsertionEffect||i.default.useLayoutEffect,_="undefined"!=typeof window?new p:void 0;function v(e){var t=_||s.useContext(m);return t&&("undefined"==typeof window?t.add(e):y(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}v.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=v},3939:(e,t,r)=>{Promise.resolve().then(r.bind(r,1944))},8375:()=>{},9137:(e,t,r)=>{"use strict";e.exports=r(2269).style},9434:(e,t,r)=>{"use strict";function n(e){return e.startsWith("http")||e.startsWith("/Portfolio")?e:"/Portfolio".concat(e)}r.d(t,{O:()=>n})}},e=>{var t=t=>e(e.s=t);e.O(0,[244,766,441,684,358],()=>t(3939)),_N_E=e.O()}]);