(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[893],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>s});var o=r(5155);function s(e){let{className:t="",children:r,variant:s="default",asChild:l=!1,onClick:n}=e,i="".concat("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2"," ").concat({default:"bg-blue-600 text-white hover:bg-blue-700 shadow-sm",outline:"border border-gray-200 hover:bg-gray-100 text-gray-900 shadow-sm",ghost:"hover:bg-gray-100 text-gray-900"}[s]," ").concat(t);return l?(0,o.jsx)("div",{className:i,onClick:n,children:r}):(0,o.jsx)("button",{className:i,onClick:n,children:r})}r(2115)},926:(e,t,r)=>{"use strict";r.d(t,{default:()=>w});var o=r(5155),s=r(2115),l=r(1359),n=r(6766),i=r(6126),a=r(285),c=r(4416),d=r(2355),u=r(3052),g=r(9099),b=r(3786),x=r(9434);function h(e){let{project:t,onClose:r}=e;(0,l.S)();let[h,p]=(0,s.useState)(0),[m,f]=(0,s.useState)(0),v=[{bg:"bg-blue-600",text:"text-blue-600",light:"bg-blue-100 text-blue-800",border:"border-blue-200",button:"bg-blue-600 hover:bg-blue-700"},{bg:"bg-green-600",text:"text-green-600",light:"bg-green-100 text-green-800",border:"border-green-200",button:"bg-green-600 hover:bg-green-700"},{bg:"bg-purple-600",text:"text-purple-600",light:"bg-purple-100 text-purple-800",border:"border-purple-200",button:"bg-purple-600 hover:bg-purple-700"},{bg:"bg-orange-600",text:"text-orange-600",light:"bg-orange-100 text-orange-800",border:"border-orange-200",button:"bg-orange-600 hover:bg-orange-700"}];(0,s.useEffect)(()=>{let e=setInterval(()=>{f(e=>(e+1)%v.length)},3e3);return()=>clearInterval(e)},[v.length]);let w=v[m],j=t.image?t.image.split(",").map(e=>e.trim()):[(0,x.O)("/images/projects/github-repo.jpg")];return(0,s.useEffect)(()=>{let e=e=>{"Escape"===e.key?r():"ArrowRight"===e.key?p(e=>(e+1)%j.length):"ArrowLeft"===e.key&&p(e=>(e-1+j.length)%j.length)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[r,j.length]),(0,s.useEffect)(()=>(document.body.style.overflow="hidden",()=>{document.body.style.overflow="auto"}),[]),(0,o.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm",children:(0,o.jsxs)("div",{className:"relative bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,o.jsxs)("div",{className:"p-4 flex justify-between items-center transition-colors duration-500 ".concat(w.bg),children:[(0,o.jsx)("h2",{className:"text-xl font-bold text-white",children:t.title}),(0,o.jsx)("button",{onClick:r,className:"text-white/80 hover:text-white transition-colors","aria-label":"Close modal",children:(0,o.jsx)(c.A,{size:24})})]}),(0,o.jsxs)("div",{className:"flex-1 overflow-y-auto p-6",children:[(0,o.jsxs)("div",{className:"relative mb-6 rounded-lg overflow-hidden bg-gray-100 h-64 md:h-96",children:[(0,o.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,o.jsx)(n.default,{src:j.length>0?j[h]:(0,x.O)("/images/projects/github-repo.jpg"),alt:"".concat(t.title," screenshot ").concat(h+1),fill:!0,className:"object-contain p-2",onError:e=>{let t=e.target;t.onerror=null,t.src=(0,x.O)("/images/projects/github-repo.jpg")}})}),j.length>1&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("button",{onClick:()=>p(e=>(e-1+j.length)%j.length),className:"absolute left-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 transition-colors","aria-label":"Previous image",children:(0,o.jsx)(d.A,{size:20})}),(0,o.jsx)("button",{onClick:()=>p(e=>(e+1)%j.length),className:"absolute right-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 transition-colors","aria-label":"Next image",children:(0,o.jsx)(u.A,{size:20})}),(0,o.jsxs)("div",{className:"absolute bottom-2 left-1/2 -translate-x-1/2 bg-black/50 text-white text-xs px-2 py-1 rounded-full",children:[h+1," / ",j.length]})]})]}),t.githubUrl&&t.githubUrl.includes("github.com")&&(0,o.jsxs)("div",{className:"flex items-center gap-2 mb-4 text-gray-700",children:[(0,o.jsx)(g.A,{size:16}),(0,o.jsx)("span",{className:"text-sm",children:"GitHub Repository"})]}),(0,o.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:t.technologies.map((e,t)=>(0,o.jsx)(i.E,{className:"transition-colors duration-500 ".concat(w.light),children:e},t))}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2 ".concat(w.text," transition-colors duration-500"),children:"About this project"}),(0,o.jsx)("p",{className:"text-gray-700 whitespace-pre-line",children:t.description})]}),t.category&&(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2 ".concat(w.text," transition-colors duration-500"),children:"Category"}),(0,o.jsx)("p",{className:"text-gray-700",children:t.category})]}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2 ".concat(w.text," transition-colors duration-500"),children:"Created"}),(0,o.jsx)("p",{className:"text-gray-700",children:new Date(t.createdAt).toLocaleDateString()})]})]}),(0,o.jsxs)("div",{className:"p-4 border-t border-gray-100 flex justify-between items-center",children:[(0,o.jsxs)("div",{className:"flex gap-4",children:[t.githubUrl&&(0,o.jsxs)("a",{href:t.githubUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-4 py-2 rounded-md transition-colors ".concat(w.text," hover:bg-gray-100"),children:[(0,o.jsx)(g.A,{size:18}),(0,o.jsx)("span",{children:"View Code"})]}),t.liveUrl&&(0,o.jsxs)("a",{href:t.liveUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-4 py-2 rounded-md ".concat(w.bg," text-white hover:opacity-90 transition-opacity transition-colors duration-500"),children:[(0,o.jsx)(b.A,{size:18}),(0,o.jsx)("span",{children:"Live Demo"})]})]}),(0,o.jsx)(a.$,{onClick:r,variant:"outline",className:"text-gray-500",children:"Close"})]})]})})}var p=r(5055);function m(e){let{projects:t,onFilterChange:r,currentColor:l}=e,[n,i]=(0,s.useState)([]),[a,d]=(0,s.useState)(!1),[u,g]=(0,s.useState)(!1),[b,x]=(0,s.useState)(!1),[h,m]=(0,s.useState)(!1),f=p.HQ.map(e=>e.name),v=e=>{let t=e.replace(/\.js$/i,"");return"javascript"===(t=(t=(t=(t=(t=t.replace(/^Express$/i,"Express.js")).replace(/^Node$/i,"Node.js")).replace(/^React$/i,"React")).replace(/^Next$/i,"Next.js")).replace(/^Tailwind$/i,"Tailwind CSS")).toLowerCase()?"JavaScript":"typescript"===t.toLowerCase()?"TypeScript":"html"===t.toLowerCase()?"HTML":"css"===t.toLowerCase()?"CSS":"mongodb"===t.toLowerCase()?"MongoDB":"postgresql"===t.toLowerCase()?"PostgreSQL":"python"===t.toLowerCase()?"Python":"java"===t.toLowerCase()?"Java":t},w=t.flatMap(e=>e.technologies.map(e=>v(e))),j=f.filter(e=>w.some(t=>t.toLowerCase()===e.toLowerCase()||t.toLowerCase().includes(e.toLowerCase())||e.toLowerCase().includes(t.toLowerCase()))),C=["JavaScript","TypeScript","React","Next.js","Node.js","Express.js","HTML","CSS","Tailwind CSS","MongoDB","PostgreSQL","Python","Java"].filter(e=>w.some(t=>t.toLowerCase()===e.toLowerCase()||t.toLowerCase().includes(e.toLowerCase())||e.toLowerCase().includes(t.toLowerCase()))),y=new Map;C.forEach(e=>{y.set(e.toLowerCase(),e)}),j.forEach(e=>{Array.from(y.keys()).some(t=>t.includes(e.toLowerCase())||e.toLowerCase().includes(t))||y.set(e.toLowerCase(),e)});let L=Array.from(y.values()).sort(),N={blue:{badge:"bg-blue-100 text-blue-800 border-blue-200",badgeActive:"bg-blue-600 text-white border-blue-600",hover:"hover:bg-blue-50",button:"bg-blue-600 hover:bg-blue-700 text-white",buttonOutline:"border-blue-600 text-blue-600 hover:bg-blue-50"},green:{badge:"bg-green-100 text-green-800 border-green-200",badgeActive:"bg-green-600 text-white border-green-600",hover:"hover:bg-green-50",button:"bg-green-600 hover:bg-green-700 text-white",buttonOutline:"border-green-600 text-green-600 hover:bg-green-50"},purple:{badge:"bg-purple-100 text-purple-800 border-purple-200",badgeActive:"bg-purple-600 text-white border-purple-600",hover:"hover:bg-purple-50",button:"bg-purple-600 hover:bg-purple-700 text-white",buttonOutline:"border-purple-600 text-purple-600 hover:bg-purple-50"},orange:{badge:"bg-orange-100 text-orange-800 border-orange-200",badgeActive:"bg-orange-600 text-white border-orange-600",hover:"hover:bg-orange-50",button:"bg-orange-600 hover:bg-orange-700 text-white",buttonOutline:"border-orange-600 text-orange-600 hover:bg-orange-50"}}[l];(0,s.useEffect)(()=>{let e=[...t];n.length>0&&(e=e.filter(e=>n.every(t=>e.technologies.some(e=>{let r=v(e);return r.toLowerCase()===t.toLowerCase()||r.toLowerCase().includes(t.toLowerCase())||t.toLowerCase().includes(r.toLowerCase())})))),(a||u||b||h)&&(e=e.filter(e=>{var t,r,o,s,l,n,i,c,d,g,x,p,m,f,w,j,C,y,L,N,k,A,S,E,M,z;if(a){let a=e.technologies.some(e=>{let t=v(e);return["React","Next.js","TypeScript","JavaScript","CSS","Tailwind CSS","HTML","Bootstrap"].some(e=>t.toLowerCase().includes(e.toLowerCase())||e.toLowerCase().includes(t.toLowerCase()))}),d=(null==(t=e.description)?void 0:t.toLowerCase().includes("ui"))||(null==(r=e.description)?void 0:r.toLowerCase().includes("user interface"))||(null==(o=e.description)?void 0:o.toLowerCase().includes("design"))||(null==(s=e.description)?void 0:s.toLowerCase().includes("animation"))||(null==(l=e.description)?void 0:l.toLowerCase().includes("responsive"))||(null==(n=e.description)?void 0:n.toLowerCase().includes("frontend"))||(null==(i=e.description)?void 0:i.toLowerCase().includes("front-end"))||(null==(c=e.description)?void 0:c.toLowerCase().includes("web"))||e.title.toLowerCase().includes("portfolio")||e.title.toLowerCase().includes("website")||e.title.toLowerCase().includes("ui")||e.title.toLowerCase().includes("web");if(a&&d)return!0}if(u)return e.title.toLowerCase().includes("portfolio")||"portfolio website"===e.title.toLowerCase()||"dataharbor"===e.title.toLowerCase()||e.title.toLowerCase().includes("data harbor")||e.githubUrl&&(e.githubUrl.toLowerCase().includes("portfolio")||e.githubUrl.toLowerCase().includes("dataharbor"));if(b){let t=e.technologies.some(e=>{let t=v(e);return["Python","TensorFlow","PyTorch","Keras","Scikit-learn","ML","AI","Machine Learning","Deep Learning","Neural Network"].some(e=>t.toLowerCase().includes(e.toLowerCase())||e.toLowerCase().includes(t.toLowerCase()))}),r=(null==(d=e.description)?void 0:d.toLowerCase().includes("ai"))||(null==(g=e.description)?void 0:g.toLowerCase().includes("ml"))||(null==(x=e.description)?void 0:x.toLowerCase().includes("artificial intelligence"))||(null==(p=e.description)?void 0:p.toLowerCase().includes("machine learning"))||(null==(m=e.description)?void 0:m.toLowerCase().includes("deep learning"))||(null==(f=e.description)?void 0:f.toLowerCase().includes("neural"))||(null==(w=e.description)?void 0:w.toLowerCase().includes("model"))||(null==(j=e.description)?void 0:j.toLowerCase().includes("predict"))||(null==(C=e.description)?void 0:C.toLowerCase().includes("algorithm"))||e.title.toLowerCase().includes("ai")||e.title.toLowerCase().includes("ml")||e.title.toLowerCase().includes("intelligence")||e.title.toLowerCase().includes("learning")||e.title.toLowerCase().includes("neural");if(t&&r)return!0}if(h){let t=e.technologies.some(e=>{let t=v(e);return["Node.js","Express.js","MongoDB","PostgreSQL","MySQL","Redis","API","Server","Java","Spring","Django","Flask","PHP","Laravel","GraphQL","REST"].some(e=>t.toLowerCase().includes(e.toLowerCase())||e.toLowerCase().includes(t.toLowerCase()))}),r=(null==(y=e.description)?void 0:y.toLowerCase().includes("api"))||(null==(L=e.description)?void 0:L.toLowerCase().includes("server"))||(null==(N=e.description)?void 0:N.toLowerCase().includes("backend"))||(null==(k=e.description)?void 0:k.toLowerCase().includes("back-end"))||(null==(A=e.description)?void 0:A.toLowerCase().includes("database"))||(null==(S=e.description)?void 0:S.toLowerCase().includes("authentication"))||(null==(E=e.description)?void 0:E.toLowerCase().includes("authorization"))||(null==(M=e.description)?void 0:M.toLowerCase().includes("rest"))||(null==(z=e.description)?void 0:z.toLowerCase().includes("graphql"))||e.title.toLowerCase().includes("api")||e.title.toLowerCase().includes("server")||e.title.toLowerCase().includes("backend")||e.title.toLowerCase().includes("service");if(t&&r)return!0}return!1})),r(e)},[n,a,u,b,h,t,r]);let k=e=>{i(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return(0,o.jsxs)("div",{className:"mb-8 animate-fadeIn",children:[(0,o.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mb-4",children:[(0,o.jsx)("span",{className:"text-gray-700 font-medium",children:"Filter by:"}),(0,o.jsx)("button",{onClick:()=>d(!a),className:"px-3 py-1.5 rounded-full text-sm font-medium border transition-colors duration-300 ".concat(a?N.badgeActive:N.badge),children:"Web & UI"}),(0,o.jsx)("button",{onClick:()=>g(!u),className:"px-3 py-1.5 rounded-full text-sm font-medium border transition-colors duration-300 ".concat(u?N.badgeActive:N.badge),children:"Vibe Coding"}),(0,o.jsx)("button",{onClick:()=>x(!b),className:"px-3 py-1.5 rounded-full text-sm font-medium border transition-colors duration-300 ".concat(b?N.badgeActive:N.badge),children:"AI & ML"}),(0,o.jsx)("button",{onClick:()=>m(!h),className:"px-3 py-1.5 rounded-full text-sm font-medium border transition-colors duration-300 ".concat(h?N.badgeActive:N.badge),children:"Backend"}),(n.length>0||a||u||b||h)&&(0,o.jsxs)("button",{onClick:()=>{i([]),d(!1),g(!1),x(!1),m(!1)},className:"px-3 py-1.5 rounded-full text-sm font-medium border border-gray-300 text-gray-700 hover:bg-gray-50 flex items-center gap-1 transition-colors duration-300",children:[(0,o.jsx)(c.A,{size:14}),"Clear filters"]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-2 mb-2",children:[(0,o.jsx)("span",{className:"text-gray-700 font-medium",children:"Technologies:"}),L.map(e=>(0,o.jsx)("button",{onClick:()=>k(e),className:"px-3 py-1 rounded-full text-sm font-medium border transition-colors duration-300 ".concat(n.includes(e)?N.badgeActive:N.badge),children:e},e))]}),(n.length>0||a||u||b||h)&&(0,o.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mt-4",children:[(0,o.jsx)("span",{className:"text-gray-700",children:"Active filters:"}),n.map(e=>(0,o.jsxs)("span",{className:"px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 ".concat(N.badgeActive),children:[e,(0,o.jsx)("button",{onClick:()=>k(e),className:"hover:bg-white/20 rounded-full p-0.5",children:(0,o.jsx)(c.A,{size:12})})]},e)),a&&(0,o.jsxs)("span",{className:"px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 ".concat(N.badgeActive),children:["Web & UI",(0,o.jsx)("button",{onClick:()=>d(!1),className:"hover:bg-white/20 rounded-full p-0.5",children:(0,o.jsx)(c.A,{size:12})})]}),u&&(0,o.jsxs)("span",{className:"px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 ".concat(N.badgeActive),children:["Vibe Coding",(0,o.jsx)("button",{onClick:()=>g(!1),className:"hover:bg-white/20 rounded-full p-0.5",children:(0,o.jsx)(c.A,{size:12})})]}),b&&(0,o.jsxs)("span",{className:"px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 ".concat(N.badgeActive),children:["AI & ML",(0,o.jsx)("button",{onClick:()=>x(!1),className:"hover:bg-white/20 rounded-full p-0.5",children:(0,o.jsx)(c.A,{size:12})})]}),h&&(0,o.jsxs)("span",{className:"px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 ".concat(N.badgeActive),children:["Backend",(0,o.jsx)("button",{onClick:()=>m(!1),className:"hover:bg-white/20 rounded-full p-0.5",children:(0,o.jsx)(c.A,{size:12})})]})]})]})}var f=r(6932),v=r(2138);function w(e){let{projects:t}=e,{currentColor:r}=(0,l.S)(),[i,a]=(0,s.useState)(null),[c,d]=(0,s.useState)(0),[u,p]=(0,s.useState)(t),[w,j]=(0,s.useState)(!1),C=[{bg:"from-blue-50 to-blue-100",accent:"bg-blue-600",text:"text-blue-600",light:"bg-blue-100 text-blue-800",border:"border-blue-200",button:"bg-blue-600 hover:bg-blue-700",hoverText:"hover:text-blue-600"},{bg:"from-green-50 to-green-100",accent:"bg-green-600",text:"text-green-600",light:"bg-green-100 text-green-800",border:"border-green-200",button:"bg-green-600 hover:bg-green-700",hoverText:"hover:text-green-600"},{bg:"from-purple-50 to-purple-100",accent:"bg-purple-600",text:"text-purple-600",light:"bg-purple-100 text-purple-800",border:"border-purple-200",button:"bg-purple-600 hover:bg-purple-700",hoverText:"hover:text-purple-600"},{bg:"from-orange-50 to-orange-100",accent:"bg-orange-600",text:"text-orange-600",light:"bg-orange-100 text-orange-800",border:"border-orange-200",button:"bg-orange-600 hover:bg-orange-700",hoverText:"hover:text-orange-600"}];(0,s.useEffect)(()=>{let e=setInterval(()=>{d(e=>(e+1)%C.length)},3e3);return()=>clearInterval(e)},[C.length]);let y=C[c],L=e=>{a(e)};return(0,s.useEffect)(()=>{p(t)},[t]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("section",{className:"py-20 bg-gradient-to-b ".concat(y.bg," transition-colors duration-500"),children:(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[(0,o.jsxs)("div",{className:"text-center mb-8",children:[(0,o.jsx)("h1",{className:"text-4xl md:text-5xl font-bold ".concat(y.text," mb-4 transition-colors duration-500"),children:"My Projects"}),(0,o.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"A collection of my work that demonstrates my skills and experience."}),(0,o.jsx)("div",{className:"w-20 h-1 ".concat(y.accent," mx-auto mt-4 transition-colors duration-500")})]}),(0,o.jsx)("div",{className:"flex justify-center mb-8",children:(0,o.jsxs)("button",{onClick:()=>j(!w),className:"flex items-center gap-2 px-4 py-2 rounded-md ".concat(y.button," text-white transition-all duration-300 transform hover:scale-105"),children:[(0,o.jsx)(f.A,{size:18}),w?"Hide Filters":"Show Filters"]})}),w&&(0,o.jsx)(m,{projects:t,onFilterChange:p,currentColor:r}),0===u.length?(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("p",{className:"text-gray-600",children:"No projects found matching your filters."}),t.length>0&&w&&(0,o.jsx)("button",{onClick:()=>p(t),className:"mt-4 px-4 py-2 rounded-md ".concat(y.button," text-white transition-colors duration-300"),children:"Clear Filters"})]}):(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:u.map(e=>(0,o.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-lg transition-all duration-300 hover:shadow-xl border border-gray-100 hover:border-gray-200 flex flex-col",children:[(0,o.jsxs)("div",{className:"relative h-48 ".concat(y.light," flex items-center justify-center overflow-hidden transition-colors duration-500"),children:[(0,o.jsx)(n.default,{src:e.image?e.image.split(",")[0].trim():(0,x.O)("/images/projects/github-repo.jpg"),alt:e.title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-500",onError:e=>{let t=e.target;t.onerror=null,t.src=(0,x.O)("/images/projects/github-repo.jpg")}}),e.githubUrl&&e.githubUrl.includes("github.com")&&(0,o.jsx)("div",{className:"absolute top-2 right-2 bg-black/70 text-white p-1.5 rounded-full",children:(0,o.jsx)(g.A,{size:16})})]}),(0,o.jsxs)("div",{className:"p-6 flex-1 flex flex-col",children:[(0,o.jsx)("h3",{className:"text-xl font-bold ".concat(y.text," mb-2 transition-colors duration-500"),children:e.title}),(0,o.jsx)("p",{className:"text-gray-700 mb-4 line-clamp-3 flex-grow",children:e.description}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.technologies.slice(0,3).map((e,t)=>(0,o.jsx)("span",{className:"px-3 py-1 ".concat(y.light," text-sm rounded-full transition-colors duration-500"),children:e},t)),e.technologies.length>3&&(0,o.jsxs)("span",{className:"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full",children:["+",e.technologies.length-3]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center mt-auto",children:[(0,o.jsxs)("div",{className:"flex space-x-3",children:[e.githubUrl&&(0,o.jsx)("a",{href:e.githubUrl,target:"_blank",rel:"noopener noreferrer",className:"text-gray-700 ".concat(y.hoverText," flex items-center transition-colors duration-300"),"aria-label":"View code on GitHub",children:(0,o.jsx)(g.A,{size:20})}),e.liveUrl&&(0,o.jsx)("a",{href:e.liveUrl,target:"_blank",rel:"noopener noreferrer",className:"text-gray-700 ".concat(y.hoverText," flex items-center transition-colors duration-300"),"aria-label":"View live demo",children:(0,o.jsx)(b.A,{size:20})})]}),(0,o.jsxs)("button",{onClick:()=>L(e),className:"".concat(y.button," text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors duration-500 transform hover:scale-105"),children:[(0,o.jsx)("span",{children:"Know More"}),(0,o.jsx)(v.A,{size:16})]})]})]})]},e._id))})]})}),i&&(0,o.jsx)(h,{project:i,onClose:()=>{a(null)}})]})}},2138:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2355:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3052:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3786:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>s});var o=r(5155);function s(e){let{className:t="",children:r,variant:s="default"}=e;return(0,o.jsx)("span",{className:"".concat("inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"," ").concat({default:"bg-blue-100 text-blue-800",outline:"border border-gray-200 text-gray-700 shadow-sm"}[s]," ").concat(t),children:r})}r(2115)},6932:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},8315:(e,t,r)=>{Promise.resolve().then(r.bind(r,3725)),Promise.resolve().then(r.bind(r,926)),Promise.resolve().then(r.bind(r,1359))},9099:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var o=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},a=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:g,...b}=e;return(0,o.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:n?24*Number(l)/Number(s):l,className:i("lucide",d),...!u&&!a(b)&&{"aria-hidden":"true"},...b},[...g.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,o.forwardRef)((r,l)=>{let{className:a,...c}=r;return(0,o.createElement)(d,{ref:l,iconNode:t,className:i("lucide-".concat(s(n(e))),"lucide-".concat(e),a),...c})});return r.displayName=n(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[244,766,725,55,441,684,358],()=>t(8315)),_N_E=e.O()}]);