(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{184:(e,s,t)=>{"use strict";t.d(s,{default:()=>u});var a=t(5155),l=t(2115),r=t(1e3),i=t(3717),n=t(1359),o=t(9946);let c=(0,o.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var m=t(4516);let d=(0,o.A)("phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);var h=t(9099),x=t(2894),g=t(5684);let u=()=>{let{currentColor:e}=(0,n.S)(),s=(0,n.g)(e),[t,o]=(0,l.useState)({name:"",email:"",contactReason:i.z.JOB_OPPORTUNITY,message:""}),[u,p]=(0,l.useState)({name:"",email:"",message:""}),[f,b]=(0,l.useState)({name:!1,email:!1,message:!1}),[v,j]=(0,l.useState)(!1),[w,y]=(0,l.useState)(null),N=e=>e.trim()?e.trim().length<2?"Name must be at least 2 characters":/^[a-zA-Z\s]+$/.test(e)?"":"Name should only contain letters and spaces":"Name is required",k=e=>e.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?"":"Please enter a valid email address":"Email is required",A=e=>e.trim()?e.trim().length<10?"Message must be at least 10 characters":"":"Message is required",C=e=>{let{name:s,value:t}=e.target;if(o(e=>({...e,[s]:t})),f[s]){let e="";"name"===s?e=N(t):"email"===s?e=k(t):"message"===s&&(e=A(t)),p(t=>({...t,[s]:e}))}},S=e=>{let{name:s,value:t}=e.target;b(e=>({...e,[s]:!0}));let a="";"name"===s?a=N(t):"email"===s?a=k(t):"message"===s&&(a=A(t)),p(e=>({...e,[s]:a}))},M=async e=>{e.preventDefault(),b({name:!0,email:!0,message:!0});let s=N(t.name),a=k(t.email),l=A(t.message);if(p({name:s,email:a,message:l}),s||a||l)return void y({success:!1,message:"Please fix the errors in the form before submitting."});j(!0),y(null);try{let e=await (0,r.RS)(t);y({success:!0,message:e.message||"Your message has been sent successfully!"}),o({name:"",email:"",contactReason:i.z.JOB_OPPORTUNITY,message:""}),b({name:!1,email:!1,message:!1}),p({name:"",email:"",message:""})}catch(e){y({success:!1,message:"Failed to send message. Please try again later."}),console.error(e)}finally{j(!1)}};return(0,a.jsxs)("section",{id:"home-contact",children:[(0,a.jsx)("div",{className:"py-12 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"text-center relative z-10",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Contact Me"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Have a question or want to work together? Feel free to reach out!"}),(0,a.jsx)("div",{className:"w-20 h-1 ".concat(s.bg," mx-auto mt-4")})]})})}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row",children:[(0,a.jsxs)("div",{className:"lg:w-1/3 py-16 px-8 relative ".concat(s.light),children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,a.jsx)("div",{className:"absolute top-0 left-0 w-64 h-64 bg-white rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"}),(0,a.jsx)("div",{className:"container mx-auto h-full flex items-center justify-center",children:(0,a.jsxs)("div",{className:"relative z-10 max-w-md mx-auto",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-8 ".concat(s.text),children:"Contact Information"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 ".concat(s.bg," p-3 rounded-full shadow-md"),children:(0,a.jsx)(c,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-gray-900",children:"Email"}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-gray-700",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 ".concat(s.bg," p-3 rounded-full shadow-md"),children:(0,a.jsx)(m.A,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-gray-900",children:"Address"}),(0,a.jsx)("p",{className:"text-gray-700",children:"A-1201, Suryam Ananta, Vastral"}),(0,a.jsx)("p",{className:"text-gray-700",children:"Ahmedabad, Gujarat, India 382418"}),(0,a.jsxs)("a",{href:"https://maps.google.com/?q=A-1201,+Suryam+Ananta,+Vastral,+Ahmedabad,+Gujarat,+India+382418",target:"_blank",rel:"noopener noreferrer",className:"mt-2 inline-flex items-center text-sm ".concat(s.text),children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-1"})," View on Map"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 ".concat(s.bg," p-3 rounded-full shadow-md"),children:(0,a.jsx)(d,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-gray-900",children:"Phone"}),(0,a.jsx)("a",{href:"tel:+************",className:"text-gray-700",children:"+91 7202906881"})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-gray-900 mb-4",children:"Connect with me"}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("a",{href:"https://github.com/AnkushGitRepo",target:"_blank",rel:"noopener noreferrer",className:"".concat(s.bg," p-3 rounded-full shadow-md transition-transform hover:scale-110"),children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("a",{href:"https://linkedin.com/in/ankushgupta18",target:"_blank",rel:"noopener noreferrer",className:"".concat(s.bg," p-3 rounded-full shadow-md transition-transform hover:scale-110"),children:(0,a.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("a",{href:"https://instagram.com/_ankushg",target:"_blank",rel:"noopener noreferrer",className:"".concat(s.bg," p-3 rounded-full shadow-md transition-transform hover:scale-110"),children:(0,a.jsx)(g.A,{className:"h-5 w-5 text-white"})})]})]})]})]})})]}),(0,a.jsxs)("div",{className:"lg:w-2/3 py-16 px-8 bg-gray-50 relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,a.jsx)("div",{className:"absolute bottom-0 right-0 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"}),(0,a.jsx)("div",{className:"container mx-auto h-full flex items-center justify-center",children:(0,a.jsxs)("div",{className:"w-full max-w-2xl mx-auto",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-8 ".concat(s.text," text-center lg:text-left"),children:"Send a Message"}),(0,a.jsxs)("form",{onSubmit:M,className:"bg-white shadow-xl rounded-xl p-8 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.01] relative z-10",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:t.name,onChange:C,onBlur:S,required:!0,className:"w-full px-4 py-2 border ".concat(u.name&&f.name?"border-red-500":"border-gray-300"," rounded-lg focus:ring-2 focus:border-transparent bg-white text-gray-900")}),u.name&&f.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",value:t.email,onChange:C,onBlur:S,required:!0,className:"w-full px-4 py-2 border ".concat(u.email&&f.email?"border-red-500":"border-gray-300"," rounded-lg focus:ring-2 focus:border-transparent bg-white text-gray-900")}),u.email&&f.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.email})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{htmlFor:"contactReason",className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason for Contact"}),(0,a.jsx)("select",{id:"contactReason",name:"contactReason",value:t.contactReason,onChange:e=>{let s=e.target.value;o(e=>({...e,contactReason:s}))},required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent bg-white text-gray-900",children:Object.values(i.z).map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,a.jsx)("textarea",{id:"message",name:"message",value:t.message,onChange:C,onBlur:S,required:!0,rows:4,className:"w-full px-4 py-2 border ".concat(u.message&&f.message?"border-red-500":"border-gray-300"," rounded-lg focus:ring-2 focus:border-transparent bg-white text-gray-900")}),u.message&&f.message&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.message})]}),w&&(0,a.jsx)("div",{className:"p-4 rounded-lg mb-6 ".concat(w.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:w.message}),(0,a.jsx)("button",{type:"submit",disabled:v,className:"w-full px-6 py-3 ".concat(s.bg," ").concat(s.hover," text-white font-medium rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 shadow-md hover:shadow-lg"),children:v?"Sending...":"Send Message"})]})]})})]})]})]})}},285:(e,s,t)=>{"use strict";t.d(s,{$:()=>l});var a=t(5155);function l(e){let{className:s="",children:t,variant:l="default",asChild:r=!1,onClick:i}=e,n="".concat("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2"," ").concat({default:"bg-blue-600 text-white hover:bg-blue-700 shadow-sm",outline:"border border-gray-200 hover:bg-gray-100 text-gray-900 shadow-sm",ghost:"hover:bg-gray-100 text-gray-900"}[l]," ").concat(s);return r?(0,a.jsx)("div",{className:n,onClick:i,children:t}):(0,a.jsx)("button",{className:n,onClick:i,children:t})}t(2115)},1e3:(e,s,t)=>{"use strict";t.d(s,{gp:()=>o,RS:()=>c});let a="http://localhost:5001/api";async function l(){let e=await fetch("".concat(a,"/skills"));if(!e.ok)throw Error("Failed to fetch skills");return e.json()}async function r(e){let s=await fetch("".concat(a,"/contact"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to submit contact form");return s.json()}var i=t(5055);let n=window.location.hostname.includes("github.io");async function o(){if(n)return i.HQ;try{return await l()}catch(e){return console.warn("Failed to fetch skills from API, using mock data",e),i.HQ}}async function c(e){if(n)return(0,i.v0)();try{return await r(e)}catch(e){return console.warn("Failed to submit contact form to API, using mock handler",e),(0,i.v0)()}}},2138:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2491:(e,s,t)=>{Promise.resolve().then(t.bind(t,3725)),Promise.resolve().then(t.bind(t,6132)),Promise.resolve().then(t.bind(t,184)),Promise.resolve().then(t.bind(t,2944)),Promise.resolve().then(t.bind(t,4938)),Promise.resolve().then(t.bind(t,1359))},2894:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},2944:(e,s,t)=>{"use strict";t.d(s,{default:()=>N});var a=t(5155);t(2115);var l=t(6874),r=t.n(l),i=t(6766);function n(e){let{className:s="",children:t}=e;return(0,a.jsx)("div",{className:"rounded-lg overflow-hidden shadow-md ".concat(s),children:t})}function o(e){let{className:s="",children:t}=e;return(0,a.jsx)("div",{className:"p-3 sm:p-4 ".concat(s),children:t})}function c(e){let{className:s="",children:t}=e;return(0,a.jsx)("h3",{className:"text-xl font-bold ".concat(s),children:t})}function m(e){let{className:s="",children:t}=e;return(0,a.jsx)("div",{className:"p-3 sm:p-4 ".concat(s),children:t})}function d(e){let{className:s="",children:t}=e;return(0,a.jsx)("div",{className:"p-3 sm:p-4 border-t border-gray-100 mt-auto ".concat(s),children:t})}var h=t(6126),x=t(285),g=t(1359);let u=e=>{let{className:s=""}=e;return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",className:s,children:(0,a.jsx)("path",{d:"M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.419 1.56-.299.421-1.02.599-1.559.3z"})})};var p=t(9434),f=t(9946);let b=(0,f.A)("code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);var v=t(2138),j=t(3786);let w=(0,f.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);var y=t(4516);function N(){let{currentColor:e}=(0,g.S)(),s=(0,g.g)(e),t=[{id:1,title:"Pharmacy Management System",description:"A console-based system using Java and PostgreSQL for managing drug inventory and transactions.",image:(0,p.O)("/images/projects/pharmacy-system.jpg"),link:"/projects/pharmacy-management-system",githubUrl:"https://github.com/AnkushGitRepo/Pharmacy-Management-System",tags:["Java","PostgreSQL","Console App"]},{id:2,title:"Currency Converter",description:"Java console application allowing users to view, convert, and update exchange rates.",image:(0,p.O)("/images/projects/currency-converter.jpg"),link:"/projects/currency-converter",githubUrl:"https://github.com/AnkushGitRepo/Currency_Converter_Using_Core_Java",tags:["Java","Console App"]},{id:3,title:"Cashflow Compass",description:"Python-based CLI Expense Tracker for managing and analyzing expenses.",image:(0,p.O)("/images/projects/cashflow-compass.jpg"),link:"/projects/cashflow-compass",githubUrl:"https://github.com/AnkushGitRepo/Cashflow-Compass",tags:["Python","CLI","Finance"]}],l=[{category:"Languages",items:[{name:"Java",icon:(0,p.O)("/images/skills/icons8-java.svg")},{name:"Python",icon:(0,p.O)("/images/skills/icons8-python.svg")},{name:"JavaScript",icon:(0,p.O)("/images/skills/icons8-javascript.svg")},{name:"HTML",icon:(0,p.O)("/images/skills/icons8-html5.svg")},{name:"CSS",icon:(0,p.O)("/images/skills/icons8-css.svg")},{name:"TypeScript",icon:(0,p.O)("/images/skills/icons8-typescript.svg")}]},{category:"Frameworks & Libraries",items:[{name:"React",icon:(0,p.O)("/images/skills/icons8-react.svg")},{name:"Node.js",icon:(0,p.O)("/images/skills/icons8-nodejs.svg")},{name:"Express.js",icon:(0,p.O)("/images/skills/icons8-express-js.svg")},{name:"Bootstrap",icon:(0,p.O)("/images/skills/icons8-bootstrap.svg")},{name:"Tailwind CSS",icon:(0,p.O)("/images/skills/icons8-tailwind-css.svg")}]},{category:"Databases",items:[{name:"MySQL",icon:(0,p.O)("/images/skills/icons8-mysql.svg")},{name:"PostgreSQL",icon:(0,p.O)("/images/skills/icons8-postgres.svg")},{name:"MongoDB",icon:(0,p.O)("/images/skills/MongoDB.svg")},{name:"Redis",icon:(0,p.O)("/images/skills/icons8-redis.svg")}]},{category:"Tools & IDEs",items:[{name:"Git",icon:(0,p.O)("/images/skills/icons8-git.svg")},{name:"GitHub",icon:(0,p.O)("/images/skills/icons8-github.svg")},{name:"VS Code",icon:(0,p.O)("/images/skills/icons8-visual-studio.svg")},{name:"IntelliJ IDEA",icon:(0,p.O)("/images/skills/icons8-intellij-idea.svg")},{name:"PyCharm",icon:(0,p.O)("/images/skills/icons8-pycharm.svg")},{name:"WebStorm",icon:(0,p.O)("/images/skills/icons8-webstorm.svg")},{name:"Jupyter",icon:(0,p.O)("/images/skills/icons8-jupyter.svg")},{name:"Figma",icon:(0,p.O)("/images/skills/icons8-figma.svg")}]}],f={title:"What I'm Reading",description:"Explore my collection of book reviews and recommendations.",link:"/books",image:(0,p.O)("/placeholder.svg")};return(0,a.jsx)("section",{id:"about",className:"py-6 bg-white",children:(0,a.jsxs)("div",{className:"container px-4 md:px-6 mx-auto",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center mb-4",children:[(0,a.jsx)("div",{className:"inline-block px-3 py-1 rounded-full text-sm font-medium mb-4 transition-colors duration-500 ".concat(s.light),children:"About Me"}),(0,a.jsx)("h2",{className:"text-3xl md:text-5xl font-bold tracking-tight text-center mb-4",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent",children:"Get to Know Me Better"})}),(0,a.jsx)("div",{className:"w-20 h-1 rounded-full mb-3 transition-colors duration-500 ".concat(s.bg)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)(n,{className:"overflow-hidden border-none bg-white shadow-md hover:shadow-xl transition-all duration-300 rounded-xl h-[min(500px,75vh)] flex flex-col",children:[(0,a.jsx)(o,{className:"transition-colors duration-500 ".concat(s.bg),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(c,{className:"text-xl font-bold text-white",children:"My Projects"}),(0,a.jsx)(b,{className:"h-6 w-6 text-white/80"})]})}),(0,a.jsx)(m,{className:"pt-4 flex-grow overflow-y-auto custom-scrollbar",children:(0,a.jsx)("div",{className:"space-y-4",children:t.map(s=>(0,a.jsx)("div",{className:"border-b border-slate-100 dark:border-slate-700 pb-4 last:border-0 last:pb-0",children:(0,a.jsxs)("div",{className:"flex items-start gap-3 flex-wrap sm:flex-nowrap",children:[(0,a.jsx)(r(),{href:s.link,className:"block relative w-16 h-16 flex-shrink-0 rounded-md overflow-hidden group mx-auto sm:mx-0 mb-2 sm:mb-0",children:(0,a.jsx)(i.default,{src:s.image,alt:s.title,width:64,height:64,className:"object-cover transition-transform group-hover:scale-105"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(r(),{href:s.link,className:"group",children:(0,a.jsx)("h3",{className:"font-medium text-slate-900 group-hover:text-blue-600 transition-colors",children:s.title})}),(0,a.jsx)("p",{className:"text-sm text-slate-500 mt-1",children:s.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1.5 mt-2",children:s.tags.map((s,t)=>(0,a.jsx)(h.E,{variant:"outline",className:"bg-".concat(e,"-100 text-").concat(e,"-700 border-").concat(e,"-200 font-medium shadow-sm"),children:s},t))})]})]})},s.id))})}),(0,a.jsx)(d,{children:(0,a.jsx)(x.$,{asChild:!0,variant:"ghost",className:"w-full justify-between group text-".concat(e,"-700 hover:text-").concat(e,"-800 font-medium text-left"),children:(0,a.jsxs)(r(),{href:"/projects",className:"flex items-center w-full",children:[(0,a.jsx)("span",{children:"View All Projects"}),(0,a.jsx)(v.A,{className:"h-4 w-4 ml-auto transition-transform group-hover:translate-x-1"})]})})})]}),(0,a.jsxs)(n,{className:"overflow-hidden border-none bg-white shadow-md hover:shadow-xl transition-all duration-300 rounded-xl h-[min(500px,75vh)] flex flex-col",children:[(0,a.jsx)(o,{className:"transition-colors duration-500 ".concat(s.bg),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(c,{className:"text-xl font-bold text-white",children:"My Music"}),(0,a.jsx)(u,{className:"h-6 w-6 text-white/80"})]})}),(0,a.jsxs)(m,{className:"pt-4 pb-0 flex-grow overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4 flex-wrap sm:flex-nowrap",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-black flex items-center justify-center overflow-hidden mx-auto sm:mx-0 mb-2 sm:mb-0",children:(0,a.jsx)(u,{className:"h-6 w-6 text-white"})}),(0,a.jsxs)("div",{className:"text-center sm:text-left w-full sm:w-auto",children:[(0,a.jsx)("h3",{className:"font-medium text-slate-900",children:"Ankush Gupta"}),(0,a.jsx)("p",{className:"text-sm text-slate-500",children:"Spotify Playlists"})]})]}),(0,a.jsx)("div",{className:"h-[calc(100%-50px)] overflow-y-auto pr-2 space-y-2 playlist-scrollbar",children:[{id:"2IXKMbLDM5RWnm4q2PC2vK",title:"\uD83D\uDC96It's a Time To Fall In Love \uD83C\uDF0C",image:"https://image-cdn-ak.spotifycdn.com/image/ab67706c0000da84c63b7cfc1f2698c31c606c3a",description:"A collection of heartfelt songs celebrating romance and connection."},{id:"0rtPduclzhKrKzJFztW6r2",title:"\uD83D\uDC96 Punjabi Emotions \uD83E\uDD70",image:"https://image-cdn-ak.spotifycdn.com/image/ab67706c0000da845ebe6ea4e9de12dcf82652d1",description:"Feel the love with passionate Punjabi songs that capture every beautiful moment."},{id:"0TAm0EgPYdYxgre0PpXOhV",title:"\uD83C\uDF42 Onesided Love ❤️‍\uD83D\uDD25",image:"https://image-cdn-ak.spotifycdn.com/image/ab67706c0000da847d3f8ef9caa55764488902ce",description:"Songs that express the bittersweet feelings of unreciprocated love."},{id:"07bjeQyRjkzC52nV0uWcHI",title:"\uD83C\uDF03Angreji_Vibes\uD83E\uDEE0",image:"https://image-cdn-ak.spotifycdn.com/image/ab67706c0000da848695a8cb551c94f4b3d168a7",description:"A mix of trendy English songs that bring good vibes and energy."},{id:"7hiHyqLVZhhAQRPqqKDIXr",title:"❤️Nostalgic Love\uD83E\uDE76",image:"https://image-cdn-ak.spotifycdn.com/image/ab67706c0000da842301e0928a95fb2b568c074a",description:"Classic love songs that bring back beautiful memories."},{id:"7Mlu2mdG5954hZvWTDy7pz",title:"\uD83C\uDF03Blessed\uD83C\uDFDE️",image:"https://image-cdn-ak.spotifycdn.com/image/ab67706c0000da84ffd11716c4ee502c32c56dde",description:"Uplifting songs that celebrate gratitude and joy in life."},{id:"1g4WFwUI5oNnLOefZYEAII",title:"\uD83D\uDC94Broked\uD83C\uDF15",image:"https://image-cdn-fa.spotifycdn.com/image/ab67706c0000da8427018c14c90330d0427847e7",description:"Heartbreaking songs that capture the pain of lost love."},{id:"2iLzRbjO9z6h6NSMoOBLjU",title:"\uD83D\uDC95Feel_Love\uD83E\uDD7A",image:"https://image-cdn-ak.spotifycdn.com/image/ab67706c0000da842b69979864b614cb94e6cadf",description:"Songs that celebrate love and bring warmth to your heart."}].map(e=>(0,a.jsx)(r(),{href:"https://open.spotify.com/playlist/".concat(e.id),target:"_blank",rel:"noopener noreferrer",className:"block group",children:(0,a.jsxs)("div",{className:"flex gap-2 p-2 rounded-lg hover:bg-slate-100 transition-colors flex-wrap sm:flex-nowrap",children:[(0,a.jsx)("div",{className:"relative w-16 h-16 flex-shrink-0 rounded-md overflow-hidden mx-auto sm:mx-0 mb-1 sm:mb-0",children:(0,a.jsx)(i.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover transition-transform group-hover:scale-105"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0 text-center sm:text-left",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900 truncate group-hover:text-blue-600 transition-colors",children:e.title}),(0,a.jsx)("p",{className:"text-xs text-slate-500 line-clamp-2",children:e.description})]})]})},e.id))})]}),(0,a.jsx)(d,{children:(0,a.jsx)(x.$,{asChild:!0,variant:"ghost",className:"w-full justify-between group text-".concat(e,"-700 hover:text-").concat(e,"-800 font-medium text-left"),children:(0,a.jsxs)(r(),{href:"https://open.spotify.com/user/31f5prtw67zjqv4zpavyh3qsxbwu",target:"_blank",rel:"noopener noreferrer",className:"flex items-center w-full",children:[(0,a.jsx)("span",{children:"View Spotify Profile"}),(0,a.jsx)(j.A,{className:"h-4 w-4 ml-auto"})]})})})]}),(0,a.jsxs)(n,{className:"overflow-hidden border-none bg-white shadow-md hover:shadow-xl transition-all duration-300 rounded-xl h-[min(500px,75vh)] flex flex-col",children:[(0,a.jsx)(o,{className:"transition-colors duration-500 ".concat(s.bg),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(c,{className:"text-xl font-bold text-white",children:"Skills & Technologies"}),(0,a.jsx)(b,{className:"h-6 w-6 text-white/80"})]})}),(0,a.jsxs)(m,{className:"pt-4 relative flex-grow overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white to-transparent pointer-events-none z-10"}),(0,a.jsx)("div",{className:"space-y-3 h-[calc(100%-10px)] overflow-y-auto pr-2 pb-2 custom-scrollbar",children:l.map((s,t)=>(0,a.jsxs)("div",{className:"border-b border-slate-100 pb-4 last:border-0 last:pb-0",children:[(0,a.jsxs)("h3",{className:"font-semibold text-".concat(e,"-600 mb-3 flex items-center"),children:[(0,a.jsx)("span",{className:"inline-block w-1.5 h-1.5 rounded-full bg-".concat(e,"-500 mr-2")}),s.category]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:s.items.map((s,t)=>(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center gap-1 w-14 sm:w-16 group opacity-0 animate-fadeIn",style:{animationDelay:"".concat(50*t,"ms")},children:[(0,a.jsx)("div",{className:"w-9 h-9 sm:w-10 sm:h-10 rounded-lg bg-slate-100 p-1.5 flex items-center justify-center transition-all duration-300 group-hover:bg-".concat(e,"-50 group-hover:shadow-md"),children:(0,a.jsx)(i.default,{src:s.icon,alt:s.name,width:24,height:24,className:"object-contain transition-transform duration-300 group-hover:scale-110"})}),(0,a.jsx)("span",{className:"text-xs text-center text-slate-700 truncate w-full transition-colors duration-300 group-hover:text-".concat(e,"-600 font-medium"),children:s.name})]},t))})]},t))})]}),(0,a.jsx)(d,{children:(0,a.jsx)(x.$,{asChild:!0,variant:"ghost",className:"w-full justify-between group text-".concat(e,"-700 hover:text-").concat(e,"-800 font-medium text-left"),children:(0,a.jsxs)(r(),{href:"https://github.com/AnkushGitRepo",className:"flex items-center w-full",children:[(0,a.jsx)("span",{children:"View GitHub Profile"}),(0,a.jsx)(v.A,{className:"h-4 w-4 ml-auto transition-transform group-hover:translate-x-1"})]})})})]}),(0,a.jsxs)(n,{className:"overflow-hidden border-none bg-white shadow-md hover:shadow-xl transition-all duration-300 rounded-xl h-[min(450px,70vh)] md:col-span-2 lg:col-span-1 flex flex-col",children:[(0,a.jsxs)("div",{className:"relative h-40 w-full overflow-hidden",children:[(0,a.jsx)(i.default,{src:(0,p.O)("/placeholder.svg"),alt:"Books placeholder",fill:!0,className:"object-cover transition-transform hover:scale-105"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end p-6",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)(h.E,{className:"bg-white/20 text-white border-none backdrop-blur-sm mb-2",children:[(0,a.jsx)(w,{className:"h-3 w-3 mr-1"}),"Reading List"]}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white",children:f.title})]})})]}),(0,a.jsxs)(m,{className:"pt-4 flex-grow overflow-y-auto custom-scrollbar",children:[(0,a.jsx)("p",{className:"text-sm text-slate-500",children:f.description}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-1 sm:grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{className:"bg-slate-100 p-3 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900 text-sm",children:"Fiction"}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"Novels, short stories, and literary works"})]}),(0,a.jsxs)("div",{className:"bg-slate-100 p-3 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900 text-sm",children:"Technology"}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"Programming, AI, and tech trends"})]}),(0,a.jsxs)("div",{className:"bg-slate-100 p-3 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900 text-sm",children:"Science"}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"Physics, astronomy, and discoveries"})]}),(0,a.jsxs)("div",{className:"bg-slate-100 p-3 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900 text-sm",children:"Philosophy"}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"Ideas, concepts, and thought experiments"})]})]})]}),(0,a.jsx)(d,{children:(0,a.jsx)(x.$,{asChild:!0,variant:"ghost",className:"w-full justify-between group text-".concat(e,"-700 hover:text-").concat(e,"-800 font-medium text-left"),children:(0,a.jsxs)(r(),{href:"/books",className:"flex items-center w-full",children:[(0,a.jsx)("span",{children:"Explore My Bookshelf"}),(0,a.jsx)(v.A,{className:"h-4 w-4 ml-auto transition-transform group-hover:translate-x-1"})]})})})]}),(0,a.jsxs)(n,{className:"overflow-hidden border-none bg-white shadow-md hover:shadow-xl transition-all duration-300 rounded-xl h-[min(450px,70vh)] md:col-span-2 lg:col-span-2 flex flex-col",children:[(0,a.jsxs)("div",{className:"relative h-60 w-full overflow-hidden",children:[(0,a.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d235013.70717963153!2d72.43965535!3d23.0201716!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x395e848aba5bd449%3A0x4fcedd11614f6516!2sAhmedabad%2C%20Gujarat!5e0!3m2!1sen!2sin!4v1651234567890!5m2!1sen!2sin",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6",children:[(0,a.jsxs)(h.E,{className:"bg-white/20 text-white border-none backdrop-blur-sm mb-2",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"Current Location"]}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white",children:"Ahmedabad, India"})]})]}),(0,a.jsxs)(m,{className:"pt-3 flex-grow overflow-y-auto custom-scrollbar",children:[(0,a.jsx)("p",{className:"text-sm text-slate-500",children:"Currently based in the vibrant city of Ahmedabad, Gujarat."}),(0,a.jsxs)("div",{className:"mt-4 flex items-center flex-wrap gap-2",children:[(0,a.jsxs)(h.E,{variant:"outline",className:"mr-2",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"23.0225\xb0 N, 72.5714\xb0 E"]}),(0,a.jsx)(h.E,{variant:"outline",children:"LJ University"})]})]}),(0,a.jsx)(d,{children:(0,a.jsx)(x.$,{asChild:!0,variant:"ghost",className:"w-full justify-between group text-".concat(e,"-700 hover:text-").concat(e,"-800 font-medium text-left"),children:(0,a.jsxs)(r(),{href:"https://maps.google.com/?q=Ahmedabad,Gujarat,India",target:"_blank",rel:"noopener noreferrer",className:"flex items-center w-full",children:[(0,a.jsx)("span",{children:"Open in Google Maps"}),(0,a.jsx)(j.A,{className:"h-4 w-4 ml-auto"})]})})})]})]})]})})}},3786:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4938:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var a=t(5155);t(2115);var l=t(6766),r=t(9434);let i=[{name:"React",icon:"/images/skills/icons8-react.svg"},{name:"TypeScript",icon:"/images/skills/icons8-typescript.svg"},{name:"JavaScript",icon:"/images/skills/icons8-javascript.svg"},{name:"Node.js",icon:"/images/skills/icons8-nodejs.svg"},{name:"Express",icon:"/images/skills/icons8-express-js.svg"},{name:"Python",icon:"/images/skills/icons8-python.svg"},{name:"Java",icon:"/images/skills/icons8-java.svg"},{name:"HTML",icon:"/images/skills/icons8-html5.svg"},{name:"CSS",icon:"/images/skills/icons8-css.svg"},{name:"Tailwind CSS",icon:"/images/skills/icons8-tailwind-css.svg"},{name:"Bootstrap",icon:"/images/skills/icons8-bootstrap.svg"},{name:"MongoDB",icon:"/images/skills/MongoDB.svg"},{name:"PostgreSQL",icon:"/images/skills/icons8-postgres.svg"},{name:"MySQL",icon:"/images/skills/icons8-mysql.svg"},{name:"Git",icon:"/images/skills/icons8-git.svg"},{name:"GitHub",icon:"/images/skills/icons8-github.svg"},{name:"VS Code",icon:"/images/skills/icons8-visual-studio.svg"},{name:"Redis",icon:"/images/skills/icons8-redis.svg"},{name:"WebStorm",icon:"/images/skills/icons8-webstorm.svg"},{name:"IntelliJ",icon:"/images/skills/icons8-intellij-idea.svg"},{name:"PyCharm",icon:"/images/skills/icons8-pycharm.svg"},{name:"Jupyter",icon:"/images/skills/icons8-jupyter.svg"},{name:"Figma",icon:"/images/skills/icons8-figma.svg"}],n=[...i,...i,...i],o=()=>(0,a.jsxs)("section",{className:"py-12 overflow-hidden bg-white relative",children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n    @keyframes marquee {\n      0% { transform: translateX(0); }\n      100% { transform: translateX(-100%); }\n    }\n\n    @keyframes marquee2 {\n      0% { transform: translateX(100%); }\n      100% { transform: translateX(0); }\n    }\n  "}}),(0,a.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,a.jsxs)("div",{className:"relative flex overflow-x-hidden py-8",children:[(0,a.jsx)("div",{className:"whitespace-nowrap flex items-center",style:{animation:"marquee 60s linear infinite"},children:n.map((e,s)=>(0,a.jsx)("div",{className:"inline-block mx-4 transition-transform hover:scale-110",children:(0,a.jsx)("div",{className:"w-14 h-14 flex items-center justify-center",children:(0,a.jsx)(l.default,{src:(0,r.O)(e.icon),alt:e.name,width:36,height:36,className:"object-contain",priority:!0,unoptimized:!0})})},"skill-".concat(s)))}),(0,a.jsx)("div",{className:"absolute top-0 whitespace-nowrap flex items-center py-8",style:{animation:"marquee2 60s linear infinite"},children:n.map((e,s)=>(0,a.jsx)("div",{className:"inline-block mx-4 transition-transform hover:scale-110",children:(0,a.jsx)("div",{className:"w-14 h-14 flex items-center justify-center",children:(0,a.jsx)(l.default,{src:(0,r.O)(e.icon),alt:e.name,width:36,height:36,className:"object-contain",priority:!0,unoptimized:!0})})},"skill-dup-".concat(s)))})]}),(0,a.jsx)("div",{className:"absolute top-0 left-0 h-full w-24 bg-gradient-to-r from-white to-transparent opacity-100 pointer-events-none z-10"}),(0,a.jsx)("div",{className:"absolute top-0 right-0 h-full w-24 bg-gradient-to-l from-white to-transparent opacity-100 pointer-events-none z-10"})]})},5684:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(5155);function l(e){let{className:s="",children:t,variant:l="default"}=e;return(0,a.jsx)("span",{className:"".concat("inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"," ").concat({default:"bg-blue-100 text-blue-800",outline:"border border-gray-200 text-gray-700 shadow-sm"}[l]," ").concat(s),children:t})}t(2115)},6132:(e,s,t)=>{"use strict";t.d(s,{default:()=>m});var a=t(5155),l=t(6874),r=t.n(l),i=t(6766),n=t(2115),o=t(1359),c=t(9434);let m=()=>{let[e,s]=(0,n.useState)(0),t=["Ankush Gupta","AI Generalist","Full Stack Developer","ML Engineer"],{setCurrentColor:l}=(0,o.S)();(0,n.useEffect)(()=>{let e=setInterval(()=>{s(e=>(e+1)%t.length)},3e3);return()=>clearInterval(e)},[t.length]);let m=[{bg:"from-blue-50 to-blue-100",text:"text-blue-600",button:"bg-blue-600 hover:bg-blue-700",profileBg:"bg-blue-200",profileText:"text-blue-800"},{bg:"from-green-50 to-green-100",text:"text-green-600",button:"bg-green-600 hover:bg-green-700",profileBg:"bg-green-200",profileText:"text-green-800"},{bg:"from-purple-50 to-purple-100",text:"text-purple-600",button:"bg-purple-600 hover:bg-purple-700",profileBg:"bg-purple-200",profileText:"text-purple-800"},{bg:"from-orange-50 to-orange-100",text:"text-orange-600",button:"bg-orange-600 hover:bg-orange-700",profileBg:"bg-orange-200",profileText:"text-orange-800"}][[0,1,2,3][e]];return(0,n.useEffect)(()=>{l(["blue","green","purple","orange"][e])},[e,l]),(0,a.jsxs)("section",{className:"relative min-h-screen flex flex-col justify-center overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 bg-gradient-to-r ".concat(m.bg," transition-colors duration-1000 ease-in-out"),children:[(0,a.jsx)("div",{className:"absolute top-0 -left-4 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"}),(0,a.jsx)("div",{className:"absolute top-0 -right-4 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"}),(0,a.jsx)("div",{className:"absolute -bottom-8 left-20 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"})]}),(0,a.jsx)("div",{className:"container mx-auto px-6 sm:px-8 py-16 relative z-10",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"md:w-1/2 mb-10 md:mb-0 md:pr-8",children:[(0,a.jsx)("div",{className:"mb-4 text-center md:text-left",children:(0,a.jsx)("span",{className:"inline-block px-4 py-1.5 bg-white bg-opacity-80 rounded-full text-sm font-semibold shadow-sm border border-gray-200 ".concat(m.text," transition-colors duration-1000"),children:"Full Stack Developer & ML Engineer"})}),(0,a.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 text-center md:text-left",children:[(0,a.jsx)("span",{className:"text-gray-900",children:"Hi, I'm "}),(0,a.jsx)("span",{className:"".concat(m.text," transition-colors duration-1000 ease-in-out"),children:t[e]})]}),(0,a.jsx)("p",{className:"text-lg sm:text-xl md:text-2xl text-gray-700 mb-8 max-w-xl mx-auto md:mx-0 text-center md:text-left",children:"I build exceptional intelligent systems that are fast, accessible, and visually appealing."}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 mb-10 justify-center md:justify-start",children:[(0,a.jsx)(r(),{href:"/projects",className:"px-6 py-3 ".concat(m.button," text-white font-medium rounded-lg transition-colors duration-1000 shadow-md hover:shadow-lg transform hover:scale-110"),children:"View My Work"}),(0,a.jsxs)("a",{href:(0,c.O)("/resume.pdf"),download:!0,className:"px-6 py-3 bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium rounded-lg transition-colors duration-1000 flex items-center shadow-md hover:shadow-lg transform hover:scale-110",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Download Resume"]})]}),(0,a.jsxs)("div",{className:"flex justify-center md:justify-start space-x-5 mx-auto md:mx-0 max-w-xs md:max-w-none",children:[(0,a.jsx)("a",{href:"https://github.com/AnkushGitRepo",target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-md hover:shadow-lg transition-colors duration-1000 border border-gray-200 hover:border-gray-300 hover:scale-110","aria-label":"GitHub Profile",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-700",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})})}),(0,a.jsx)("a",{href:"https://www.linkedin.com/in/ankushgupta18/",target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-md hover:shadow-lg transition-colors duration-1000 border border-gray-200 hover:border-gray-300 hover:scale-110","aria-label":"LinkedIn Profile",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-700",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"})})}),(0,a.jsx)("a",{href:"https://www.instagram.com/_ankushg/",target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-md hover:shadow-lg transition-colors duration-1000 border border-gray-200 hover:border-gray-300 hover:scale-110","aria-label":"Instagram Profile",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-700",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-md hover:shadow-lg transition-colors duration-1000 border border-gray-200 hover:border-gray-300 hover:scale-110","aria-label":"Email Contact",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-700",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})})]})]}),(0,a.jsx)("div",{className:"md:w-1/2 flex justify-center md:justify-end",children:(0,a.jsx)("div",{className:"relative w-64 h-64 md:w-80 md:h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-white shadow-xl",children:(0,a.jsx)("div",{className:"w-full h-full ".concat(m.profileBg," transition-colors duration-1000 relative"),children:(0,a.jsx)(i.default,{src:(0,c.O)("/images/profile_icon_image.png"),alt:"Ankush Gupta",fill:!0,sizes:"(max-width: 768px) 256px, (max-width: 1024px) 320px, 384px",priority:!0,className:"object-cover"})})})})]})}),(0,a.jsxs)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center z-10",children:[(0,a.jsx)("span",{className:"text-gray-600 mb-2",children:"Scroll Down"}),(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 animate-bounce text-gray-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})]})]})}},9099:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>d});var a=t(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=r(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},o=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:l=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:m="",children:d,iconNode:h,...x}=e;return(0,a.createElement)("svg",{ref:s,...c,width:l,height:l,stroke:t,strokeWidth:i?24*Number(r)/Number(l):r,className:n("lucide",m),...!d&&!o(x)&&{"aria-hidden":"true"},...x},[...h.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(d)?d:[d]])}),d=(e,s)=>{let t=(0,a.forwardRef)((t,r)=>{let{className:o,...c}=t;return(0,a.createElement)(m,{ref:r,iconNode:s,className:n("lucide-".concat(l(i(e))),"lucide-".concat(e),o),...c})});return t.displayName=i(e),t}}},e=>{var s=s=>e(e.s=s);e.O(0,[244,766,725,55,441,684,358],()=>s(2491)),_N_E=e.O()}]);