(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{1911:(e,t,a)=>{"use strict";a.d(t,{default:()=>d});var o=a(5155),r=a(2115),i=a(1359),l=a(6766),n=a(9434),s=a(2355),c=a(3052);let d=()=>{let{currentColor:e}=(0,i.S)(),[t,a]=(0,r.useState)(0),[d,u]=(0,r.useState)(0),g=[{src:"/images/img_1.jpg",position:"object-[center_30%]"},{src:"/images/img_2.jpeg",position:"object-[center_45%]"},{src:"/images/img_3.jpeg",position:"object-center"},{src:"/images/Img_4.jpeg",position:"object-[center_25%]"}],h=(0,r.useCallback)(()=>{u(e=>e===g.length-1?0:e+1)},[g.length]),b=(0,r.useCallback)(()=>{u(e=>0===e?g.length-1:e-1)},[g.length]);(0,r.useEffect)(()=>{let e=setInterval(()=>{h()},5e3);return()=>clearInterval(e)},[h]);let m=[{bg:"from-blue-50 to-blue-100",accent:"bg-blue-600",photoBg:"bg-blue-100",photoText:"text-blue-800",headingText:"text-blue-600",profileBg:"bg-blue-100",profileText:"text-blue-800"},{bg:"from-green-50 to-green-100",accent:"bg-green-600",photoBg:"bg-green-100",photoText:"text-green-800",headingText:"text-green-600",profileBg:"bg-green-100",profileText:"text-green-800"},{bg:"from-purple-50 to-purple-100",accent:"bg-purple-600",photoBg:"bg-purple-100",photoText:"text-purple-800",headingText:"text-purple-600",profileBg:"bg-purple-100",profileText:"text-purple-800"},{bg:"from-orange-50 to-orange-100",accent:"bg-orange-600",photoBg:"bg-orange-100",photoText:"text-orange-800",headingText:"text-orange-600",profileBg:"bg-orange-100",profileText:"text-orange-800"}];(0,r.useEffect)(()=>{let e=setInterval(()=>{a(e=>(e+1)%m.length)},3e3);return()=>clearInterval(e)},[m.length]);let p=m[t];return(0,o.jsxs)("section",{className:"py-20 bg-gradient-to-br ".concat(p.bg," transition-colors duration-500 relative overflow-hidden"),id:"about",children:[(0,o.jsx)("div",{className:"absolute top-20 left-20 w-64 h-64 rounded-full bg-white opacity-20 blur-3xl animate-blob"}),(0,o.jsx)("div",{className:"absolute bottom-40 right-20 w-72 h-72 rounded-full bg-white opacity-20 blur-3xl animate-blob animation-delay-2000"}),(0,o.jsx)("div",{className:"absolute top-1/2 left-1/3 w-56 h-56 rounded-full bg-white opacity-20 blur-3xl animate-blob animation-delay-4000"}),(0,o.jsx)("div",{className:"absolute bottom-20 left-1/4 w-48 h-48 rounded-full bg-white opacity-20 blur-3xl animate-blob animation-delay-3000"}),(0,o.jsx)("div",{className:"absolute top-40 right-1/4 w-60 h-60 rounded-full bg-white opacity-15 blur-3xl animate-blob animation-delay-1000"}),(0,o.jsx)("div",{className:"absolute bottom-1/3 left-10 w-52 h-52 rounded-full bg-white opacity-15 blur-3xl animate-blob animation-delay-5000"}),(0,o.jsx)("div",{className:"absolute top-10 right-10 w-40 h-40 rounded-full bg-white opacity-10 blur-3xl animate-blob animation-delay-6000"}),(0,o.jsx)("div",{className:"absolute inset-0 bg-noise opacity-[0.03] mix-blend-overlay pointer-events-none"}),(0,o.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,o.jsxs)("div",{className:"text-center mb-16",children:[(0,o.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4 transition-colors duration-300",children:"About Me"}),(0,o.jsx)("div",{className:"w-20 h-1 ".concat(p.accent," mx-auto transition-colors duration-500")})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12 items-center",children:[(0,o.jsx)("div",{className:"relative h-96 rounded-lg overflow-hidden shadow-xl ".concat(p.photoBg," transition-colors duration-500 backdrop-blur-sm bg-opacity-80"),children:(0,o.jsxs)("div",{className:"relative w-full h-full",children:[g.map((e,t)=>(0,o.jsx)("div",{className:"absolute inset-0 transition-opacity duration-1000 ".concat(t===d?"opacity-100":"opacity-0"),children:(0,o.jsx)("div",{className:"relative w-full h-full overflow-hidden",children:(0,o.jsx)(l.default,{src:(0,n.O)(e.src),alt:"Slide ".concat(t+1),fill:!0,className:"object-cover ".concat(e.position," transition-transform duration-700 hover:scale-105"),priority:0===t})})},t)),(0,o.jsx)("button",{onClick:b,className:"absolute left-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full z-10 transition-colors duration-300","aria-label":"Previous slide",children:(0,o.jsx)(s.A,{size:24})}),(0,o.jsx)("button",{onClick:h,className:"absolute right-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full z-10 transition-colors duration-300","aria-label":"Next slide",children:(0,o.jsx)(c.A,{size:24})}),(0,o.jsx)("div",{className:"absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-10",children:g.map((e,t)=>(0,o.jsx)("button",{onClick:()=>u(t),className:"w-2 h-2 rounded-full transition-all duration-300 ".concat(t===d?"w-4 ".concat(p.accent):"bg-white/70"),"aria-label":"Go to slide ".concat(t+1)},t))})]})}),(0,o.jsxs)("div",{className:"relative z-10 p-6 rounded-lg bg-white/30 backdrop-blur-sm shadow-lg",children:[(0,o.jsx)("h3",{className:"text-2xl font-bold ".concat(p.headingText," mb-4 transition-colors duration-500"),children:"ML Engineer & Full Stack Developer"}),(0,o.jsx)("p",{className:"text-gray-700 mb-6 transition-colors duration-300",children:"I'm a passionate ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications. With a strong foundation in both machine learning and web development, I create solutions that are not only technically sound but also deliver exceptional user experiences."}),(0,o.jsx)("p",{className:"text-gray-700 mb-6 transition-colors duration-300",children:"My journey in technology began with a deep curiosity about how things work, which led me to pursue a degree in Computer Science. Since then, I've worked on various projects ranging from predictive analytics systems to responsive web applications."}),(0,o.jsx)("p",{className:"text-gray-700 mb-8 transition-colors duration-300",children:"When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or sharing my knowledge through technical writing and mentoring."}),(0,o.jsx)("div",{className:"mt-6",children:(0,o.jsxs)("div",{className:"p-4 rounded-lg ".concat(p.profileBg," transition-colors duration-500 max-w-md mx-auto backdrop-blur-sm bg-opacity-80 shadow-lg"),children:[(0,o.jsx)("h4",{className:"text-lg font-semibold ".concat(p.profileText," mb-3 transition-colors duration-500"),children:"Education"}),(0,o.jsx)("div",{className:"space-y-4",children:(0,o.jsxs)("div",{className:"flex flex-col items-center space-y-3",children:[(0,o.jsx)("a",{href:"https://ljku.edu.in/",target:"_blank",rel:"noopener noreferrer",className:"block transition-transform hover:scale-105 duration-300",children:(0,o.jsx)(l.default,{src:(0,n.O)("/images/LJ_Logo.png"),alt:"LJ University Logo",width:120,height:120,className:"rounded-lg"})}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"text-gray-800 font-medium",children:"BE in Computer Science And Information Technology (CSIT)"}),(0,o.jsx)("div",{className:"text-sm text-gray-700",children:"LJ University, 2024-Present"}),(0,o.jsx)("a",{href:"https://ljku.edu.in/",target:"_blank",rel:"noopener noreferrer",className:"text-sm ".concat(p.headingText," hover:underline mt-1 inline-block transition-colors duration-300"),children:"Visit University Website"})]})]})})]})})]})]}),(0,o.jsx)("div",{className:"pb-8"})]})]})}},2355:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});let o=(0,a(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3052:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});let o=(0,a(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8759:(e,t,a)=>{Promise.resolve().then(a.bind(a,3725)),Promise.resolve().then(a.bind(a,1911)),Promise.resolve().then(a.bind(a,1359))},9434:(e,t,a)=>{"use strict";function o(e){return e.startsWith("http")||e.startsWith("/Portfolio")?e:"/Portfolio".concat(e)}a.d(t,{O:()=>o})},9946:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var o=a(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,o.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:d="",children:u,iconNode:g,...h}=e;return(0,o.createElement)("svg",{ref:t,...c,width:r,height:r,stroke:a,strokeWidth:l?24*Number(i)/Number(r):i,className:n("lucide",d),...!u&&!s(h)&&{"aria-hidden":"true"},...h},[...g.map(e=>{let[t,a]=e;return(0,o.createElement)(t,a)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let a=(0,o.forwardRef)((a,i)=>{let{className:s,...c}=a;return(0,o.createElement)(d,{ref:i,iconNode:t,className:n("lucide-".concat(r(l(e))),"lucide-".concat(e),s),...c})});return a.displayName=l(e),a}}},e=>{var t=t=>e(e.s=t);e.O(0,[244,766,725,441,684,358],()=>t(8759)),_N_E=e.O()}]);